using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace _CashDisbursement.Utilities
{
    public class AuthorizeAttribute : ActionFilterAttribute
    {
        private readonly string[]? _roles;
        private readonly bool _requireCompanyUser;
        private readonly bool _requireAdminUser;
        
        public AuthorizeAttribute(params string[] roles)
        {
            _roles = roles;
        }
        
        public AuthorizeAttribute(bool requireCompanyUser = false, bool requireAdminUser = false)
        {
            _requireCompanyUser = requireCompanyUser;
            _requireAdminUser = requireAdminUser;
        }
        
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var session = context.HttpContext.Session;
            
            if (!SessionHelper.IsAuthenticated(session))
            {
                context.Result = new RedirectToActionResult("Login", "Auth", null);
                return;
            }
            
            var user = SessionHelper.GetUser(session);
            if (user == null)
            {
                context.Result = new RedirectToActionResult("Login", "Auth", null);
                return;
            }
            
            // Check specific role requirements
            if (_roles != null && _roles.Length > 0)
            {
                if (!_roles.Contains(user.Role))
                {
                    context.Result = new ForbidResult();
                    return;
                }
            }
            
            // Check company user requirement
            if (_requireCompanyUser && !SessionHelper.IsCompanyUser(session))
            {
                context.Result = new ForbidResult();
                return;
            }
            
            // Check admin user requirement
            if (_requireAdminUser && !SessionHelper.IsAdminUser(session))
            {
                context.Result = new ForbidResult();
                return;
            }
            
            base.OnActionExecuting(context);
        }
    }
}
