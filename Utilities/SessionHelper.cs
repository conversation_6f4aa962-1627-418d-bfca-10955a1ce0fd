using _CashDisbursement.Models.Entities;
using System.Text.Json;

namespace _CashDisbursement.Utilities
{
    public static class SessionHelper
    {
        private const string UserSessionKey = "CurrentUser";
        private const string CompanySessionKey = "CurrentCompany";
        
        public static void SetUser(ISession session, User user)
        {
            var userJson = JsonSerializer.Serialize(user);
            session.SetString(UserSessionKey, userJson);
        }
        
        public static User? GetUser(ISession session)
        {
            var userJson = session.GetString(UserSessionKey);
            if (string.IsNullOrEmpty(userJson))
                return null;
            
            try
            {
                return JsonSerializer.Deserialize<User>(userJson);
            }
            catch
            {
                return null;
            }
        }
        
        public static void SetCompany(ISession session, Company company)
        {
            var companyJson = JsonSerializer.Serialize(company);
            session.SetString(CompanySessionKey, companyJson);
        }
        
        public static Company? GetCompany(ISession session)
        {
            var companyJson = session.GetString(CompanySessionKey);
            if (string.IsNullOrEmpty(companyJson))
                return null;
            
            try
            {
                return JsonSerializer.Deserialize<Company>(companyJson);
            }
            catch
            {
                return null;
            }
        }
        
        public static void ClearSession(ISession session)
        {
            session.Remove(UserSessionKey);
            session.Remove(CompanySessionKey);
            session.Clear();
        }
        
        public static bool IsAuthenticated(ISession session)
        {
            return GetUser(session) != null;
        }
        
        public static bool IsCompanyUser(ISession session)
        {
            var user = GetUser(session);
            return user != null && (user.Role == UserRoles.CompanyAdmin || user.Role == UserRoles.CompanyUser);
        }
        
        public static bool IsAdminUser(ISession session)
        {
            var user = GetUser(session);
            return user != null && (user.Role == UserRoles.SystemAdmin || user.Role == UserRoles.Approver || user.Role == UserRoles.Disbursement);
        }
    }
}
