using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.Entities
{
    public class Acquittal
    {
        public int AcquittalID { get; set; }
        
        public int ApplicationID { get; set; }
        
        // Navigation properties
        public virtual Application Application { get; set; } = null!;
        public virtual ICollection<AcquittalSubmission> AcquittalSubmissions { get; set; } = new List<AcquittalSubmission>();
    }
}
