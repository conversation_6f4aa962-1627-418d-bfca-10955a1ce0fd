using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.Entities
{
    public class AcquittalSubmission
    {
        public int SubmissionID { get; set; }
        
        public int AcquittalID { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public double Amount { get; set; }
        
        [StringLength(500)]
        public string? PDFLocation { get; set; }
        
        public DateTime Date { get; set; } = DateTime.Now;
        
        [Required]
        [StringLength(255)]
        public string SubmittedBy { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual Acquittal Acquittal { get; set; } = null!;
    }
}
