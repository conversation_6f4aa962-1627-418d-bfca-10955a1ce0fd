using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.Entities
{
    public class User
    {
        public int UserID { get; set; }
        
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;
        
        public bool Status { get; set; } = true;
        
        public int? CompanyID { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Role { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual Company? Company { get; set; }
        public virtual ICollection<Log> Logs { get; set; } = new List<Log>();
    }
    
    public static class UserRoles
    {
        public const string CompanyAdmin = "CompanyAdmin";
        public const string CompanyUser = "CompanyUser";
        public const string SystemAdmin = "SystemAdmin";
        public const string Approver = "Approver";
        public const string Disbursement = "Disbursement";
    }
}
