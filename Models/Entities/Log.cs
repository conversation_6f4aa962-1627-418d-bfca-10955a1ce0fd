using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.Entities
{
    public class Log
    {
        public int LogID { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime Date { get; set; } = DateTime.Now;
        
        public int UserID { get; set; }
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
    }
}
