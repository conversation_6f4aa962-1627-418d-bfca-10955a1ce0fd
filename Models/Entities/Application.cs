using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.Entities
{
    public class Application
    {
        public int ApplicationID { get; set; }
        
        public int CompanyID { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Purpose { get; set; } = string.Empty;
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Requested cash must be greater than 0")]
        public double RequestedCash { get; set; }
        
        public bool IsDisbursed { get; set; } = false;
        
        [StringLength(255)]
        public string? DisbursedBy { get; set; }
        
        public DateTime DateRequested { get; set; } = DateTime.Now;
        
        public DateTime? DateDisbursed { get; set; }
        
        public bool Status { get; set; } = true;
        
        public int ApprovalLevel { get; set; } = 0;
        
        [StringLength(500)]
        public string? ApprovalComment { get; set; }
        
        [StringLength(500)]
        public string? PDFLocation { get; set; }
        
        // Navigation properties
        public virtual Company Company { get; set; } = null!;
        public virtual ICollection<Acquittal> Acquittals { get; set; } = new List<Acquittal>();
    }
    
    public static class ApplicationStatus
    {
        public const int Pending = 0;
        public const int Level1Approved = 1;
        public const int Level2Approved = 2;
        public const int Level3Approved = 3;
        public const int FullyApproved = 4;
        public const int Rejected = -1;
        public const int Disbursed = 5;
    }
}
