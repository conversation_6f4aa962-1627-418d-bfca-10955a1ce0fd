using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Models.ViewModels
{
    public class CompanyDashboardViewModel
    {
        public string CompanyName { get; set; } = string.Empty;
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ApprovedApplications { get; set; }
        public int DisbursedApplications { get; set; }
        public double TotalRequestedAmount { get; set; }
        public double TotalDisbursedAmount { get; set; }
        public List<ApplicationViewModel> RecentApplications { get; set; } = new();
        public List<ApplicationViewModel> PendingAcquittals { get; set; } = new();
    }
    
    public class AdminDashboardViewModel
    {
        public int TotalCompanies { get; set; }
        public int ActiveCompanies { get; set; }
        public int TotalApplications { get; set; }
        public int PendingApprovals { get; set; }
        public int PendingDisbursements { get; set; }
        public int PendingAcquittals { get; set; }
        public double TotalRequestedAmount { get; set; }
        public double TotalDisbursedAmount { get; set; }
        public List<ApplicationViewModel> RecentApplications { get; set; } = new();
        public List<Company> RecentCompanies { get; set; } = new();
    }
}
