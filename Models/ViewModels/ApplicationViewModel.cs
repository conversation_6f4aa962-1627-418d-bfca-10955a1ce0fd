using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace _CashDisbursement.Models.ViewModels
{
    public class ApplicationViewModel
    {
        public int ApplicationID { get; set; }
        
        [Required(ErrorMessage = "Description is required")]
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string Description { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Purpose is required")]
        [StringLength(500, ErrorMessage = "Purpose cannot exceed 500 characters")]
        public string Purpose { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Requested cash amount is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Requested cash must be greater than 0")]
        [Display(Name = "Requested Cash Amount")]
        public double RequestedCash { get; set; }
        
        [Display(Name = "Supporting Document")]
        public IFormFile? SupportingDocument { get; set; }
        
        // Read-only properties for display
        public string? CompanyName { get; set; }
        public DateTime DateRequested { get; set; }
        public DateTime? DateDisbursed { get; set; }
        public bool IsDisbursed { get; set; }
        public string? DisbursedBy { get; set; }
        public int ApprovalLevel { get; set; }
        public string? ApprovalComment { get; set; }
        public string? PDFLocation { get; set; }
        public bool Status { get; set; }
        
        public string StatusText => GetStatusText();
        
        private string GetStatusText()
        {
            if (!Status) return "Rejected";
            if (IsDisbursed) return "Disbursed";
            
            return ApprovalLevel switch
            {
                0 => "Pending Review",
                1 => "Level 1 Approved",
                2 => "Level 2 Approved", 
                3 => "Level 3 Approved",
                4 => "Fully Approved - Pending Disbursement",
                _ => "Unknown Status"
            };
        }
    }
}
