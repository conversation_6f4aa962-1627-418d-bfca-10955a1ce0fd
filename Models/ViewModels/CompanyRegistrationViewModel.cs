using System.ComponentModel.DataAnnotations;

namespace _CashDisbursement.Models.ViewModels
{
    public class CompanyRegistrationViewModel
    {
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(255, ErrorMessage = "Company name cannot exceed 255 characters")]
        public string CompanyName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Company email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string CompanyEmail { get; set; } = string.Empty;
        
        [StringLength(15, ErrorMessage = "Contact cannot exceed 15 characters")]
        public string Contact { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;
        
        // Admin User Details
        [Required(ErrorMessage = "Admin email is required")]
        [EmailAddress(ErrorMessage = "Invalid admin email format")]
        [StringLength(255, ErrorMessage = "Admin email cannot exceed 255 characters")]
        public string AdminEmail { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
        [DataType(DataType.Password)]
        public string Password { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Password confirmation is required")]
        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "Password and confirmation password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
