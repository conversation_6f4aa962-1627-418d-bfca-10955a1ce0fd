# Zimdef Cash Disbursement System

A comprehensive dual-portal cash disbursement management system built with ASP.NET Core MVC and Bootstrap 5.

## Features

### Company Portal
- **User Registration & Authentication**: Companies can register and create admin accounts
- **Fund Application Management**: Submit, track, and manage fund applications
- **Application Status Tracking**: Real-time status updates with approval workflow visualization
- **Document Management**: Upload and download supporting documents
- **Dashboard Analytics**: Overview of applications, amounts, and status
- **Acquittal Submission**: Submit acquittal documentation for disbursed funds

### Admin Portal (Zimdef Portal)
- **Application Review**: Multi-level approval workflow management
- **Company Management**: Oversee registered companies and their users
- **Fund Disbursement**: Manual disbursement processing for approved applications
- **Acquittal Review**: Review and approve submitted acquittals
- **System Analytics**: Comprehensive reporting and monitoring dashboards
- **User Management**: Manage system users and roles

## Technology Stack

- **Backend**: ASP.NET Core 9.0 MVC
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Database**: SQL Server with custom data access layer
- **Email**: MailKit for SMTP email notifications
- **Authentication**: Custom authentication system with role-based access control
- **File Upload**: Support for PDF document uploads

## Architecture

### Custom Data Access Layer
- No Entity Framework - custom repository pattern implementation
- Direct SQL queries with parameterized commands
- Separation of concerns with dedicated service classes

### Dual Portal Design
- **Company Portal**: `/CompanyDashboard`, `/Application` routes
- **Admin Portal**: `/AdminDashboard`, `/AdminApplication` routes
- Role-based navigation and access control

### Security Features
- Custom authentication with session management
- Role-based authorization attributes
- SQL injection prevention with parameterized queries
- File upload validation and security

## Database Schema

The system uses the following main entities:
- **Company**: Registered companies
- **Users**: System users with role-based access
- **Applications**: Fund applications with approval workflow
- **Acquittals**: Acquittal records for disbursed funds
- **AcquittalSubmissions**: Detailed acquittal submissions
- **ApprovalList**: Approval workflow configuration
- **Logs**: System activity logging

## Setup Instructions

### Prerequisites
- .NET 9.0 SDK
- SQL Server (LocalDB, Express, or Full)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd _CashDisbursement
   ```

2. **Database Setup**
   - Create a new SQL Server database named `ZimdefCashDisbursement`
   - Run the schema creation script from `Models/schema.txt`
   - Optionally run `Database/TestData.sql` for sample data

3. **Configuration**
   - Update `appsettings.json` with your database connection string:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=ZimdefCashDisbursement;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```
   - Configure email settings in `appsettings.json` for notifications

4. **Run the Application**
   ```bash
   dotnet restore
   dotnet build
   dotnet run
   ```

5. **Access the Application**
   - Navigate to `https://localhost:5001` or `http://localhost:5000`
   - Use test credentials from `Database/TestData.sql` if sample data was loaded

## Test Credentials

If you run the test data script, you can use these credentials:

### Company Portal
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Company Admin

### Admin Portal
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: System Administrator

## Application Workflow

### Fund Application Process
1. **Submission**: Company submits application with supporting documents
2. **Multi-Level Approval**: Applications go through 3 approval levels
3. **Disbursement**: Approved applications are manually disbursed by authorized personnel
4. **Acquittal**: Companies submit acquittal documentation
5. **Completion**: Acquittals are reviewed and approved

### Email Notifications
- Application submission notifications to approvers
- Approval/rejection notifications to companies
- Disbursement confirmation emails
- Acquittal request notifications

## Project Structure

```
_CashDisbursement/
├── Controllers/           # MVC Controllers
├── Data/                 # Database connection and repositories
├── Models/               # Entity models and view models
├── Services/             # Business logic services
├── Utilities/            # Helper classes and attributes
├── Views/                # Razor views
├── wwwroot/              # Static files (CSS, JS, uploads)
├── Database/             # SQL scripts
└── README.md
```

## Key Features Implementation

### Custom Authentication
- Session-based authentication without ASP.NET Core Identity
- Role-based access control with custom attributes
- Secure password handling (note: implement proper hashing in production)

### File Management
- PDF upload support for supporting documents
- Secure file storage in `wwwroot/uploads`
- File download with access control

### Responsive Design
- Bootstrap 5 implementation
- Mobile-friendly interface
- Modern UI with gradient designs and animations

## Development Notes

### Security Considerations
- Implement proper password hashing (currently using plain text for demo)
- Add CSRF protection for forms
- Implement proper file upload validation
- Add rate limiting for login attempts

### Production Deployment
- Update connection strings for production database
- Configure proper email SMTP settings
- Implement proper logging and error handling
- Add SSL/TLS certificates
- Configure proper backup strategies

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
