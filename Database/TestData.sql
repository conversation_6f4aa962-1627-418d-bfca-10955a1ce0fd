-- Test Data for Zimdef Cash Disbursement System
-- Run this script after creating the database schema

-- Insert test companies
INSERT INTO Company (Name, Email, Contact, Address, Status) VALUES
('ABC Manufacturing Ltd', '<EMAIL>', '+263712345678', '123 Industrial Road, Harare', 1),
('XYZ Services Pvt Ltd', '<EMAIL>', '+263723456789', '456 Business Park, Bulawayo', 1),
('Tech Solutions Inc', '<EMAIL>', '+263734567890', '789 Innovation Hub, Gweru', 1);

-- Insert test users
-- Company Admin Users
INSERT INTO Users (Email, Password, Status, CompanyID, Role) VALUES
('<EMAIL>', 'password123', 1, 1, 'CompanyAdmin'),
('<EMAIL>', 'password123', 1, 2, 'CompanyAdmin'),
('<EMAIL>', 'password123', 1, 3, 'CompanyAdmin');

-- Company Regular Users
INSERT INTO Users (Email, Password, Status, CompanyID, Role) VALUES
('<EMAIL>', 'password123', 1, 1, 'CompanyUser'),
('<EMAIL>', 'password123', 1, 2, 'CompanyUser');

-- System Admin Users
INSERT INTO Users (Email, Password, Status, CompanyID, Role) VALUES
('<EMAIL>', 'admin123', 1, NULL, 'SystemAdmin'),
('<EMAIL>', 'approver123', 1, NULL, 'Approver'),
('<EMAIL>', 'approver123', 1, NULL, 'Approver'),
('<EMAIL>', 'disburse123', 1, NULL, 'Disbursement');

-- Insert approval list
INSERT INTO ApprovalList (email, approverNum) VALUES
('<EMAIL>', 1),
('<EMAIL>', 2),
('<EMAIL>', 3);

-- Insert test applications
INSERT INTO Applications (CompanyID, Description, Purpose, RequestedCash, IsDisbursed, DateRequested, Status, ApprovalLevel) VALUES
(1, 'Equipment Purchase for Production Line', 'Purchase of new manufacturing equipment to increase production capacity', 50000.00, 0, GETDATE()-10, 1, 2),
(1, 'Working Capital for Q1 Operations', 'Additional working capital needed for Q1 2025 operations and inventory', 25000.00, 1, GETDATE()-30, 1, 5),
(2, 'Office Renovation Project', 'Renovation of main office building including new furniture and equipment', 15000.00, 0, GETDATE()-5, 1, 1),
(2, 'Staff Training and Development', 'Professional development courses for technical staff members', 8000.00, 0, GETDATE()-15, 1, 3),
(3, 'Software License Renewal', 'Annual renewal of enterprise software licenses for development tools', 12000.00, 1, GETDATE()-45, 1, 5);

-- Update some applications with disbursement information
UPDATE Applications 
SET IsDisbursed = 1, DisbursedBy = '<EMAIL>', DateDisbursed = GETDATE()-20, ApprovalLevel = 5
WHERE ApplicationID IN (2, 5);

-- Insert some approval comments
UPDATE Applications 
SET ApprovalComment = 'Approved for equipment purchase. Ensure proper documentation of purchase orders.'
WHERE ApplicationID = 1;

UPDATE Applications 
SET ApprovalComment = 'Approved for working capital. Monitor cash flow carefully.'
WHERE ApplicationID = 2;

UPDATE Applications 
SET ApprovalComment = 'Approved for office renovation. Submit receipts for all expenses.'
WHERE ApplicationID = 3;

-- Insert some log entries
INSERT INTO Logs (Description, Date, UserID) VALUES
('User logged in', GETDATE()-1, 1),
('Created new application for equipment purchase', GETDATE()-10, 1),
('Application approved at level 1', GETDATE()-9, 7),
('Application approved at level 2', GETDATE()-8, 8),
('User logged in', GETDATE()-5, 3),
('Created new application for office renovation', GETDATE()-5, 3),
('Application approved at level 1', GETDATE()-4, 7),
('User logged in', GETDATE()-2, 2),
('Created new application for working capital', GETDATE()-30, 2),
('Application disbursed', GETDATE()-20, 9);

-- Create acquittals for disbursed applications
INSERT INTO Acquittals (ApplicationID) VALUES (2), (5);

-- Insert acquittal submissions
INSERT INTO AcquittalSubmissions (AcquittalID, Description, Amount, Date, SubmittedBy) VALUES
(1, 'Purchase of raw materials and inventory for Q1 operations', 25000.00, GETDATE()-10, '<EMAIL>'),
(2, 'Software license payments and setup costs', 12000.00, GETDATE()-15, '<EMAIL>');

-- Display summary of test data
SELECT 'Companies' as DataType, COUNT(*) as Count FROM Company
UNION ALL
SELECT 'Users', COUNT(*) FROM Users
UNION ALL
SELECT 'Applications', COUNT(*) FROM Applications
UNION ALL
SELECT 'Acquittals', COUNT(*) FROM Acquittals
UNION ALL
SELECT 'Logs', COUNT(*) FROM Logs;

-- Display login credentials for testing
SELECT 
    'LOGIN CREDENTIALS FOR TESTING' as Info,
    '' as Email,
    '' as Password,
    '' as Role
UNION ALL
SELECT 
    '================================',
    '',
    '',
    ''
UNION ALL
SELECT 
    'Company Admin (ABC Manufacturing)',
    '<EMAIL>',
    'password123',
    'CompanyAdmin'
UNION ALL
SELECT 
    'Company Admin (XYZ Services)',
    '<EMAIL>',
    'password123',
    'CompanyAdmin'
UNION ALL
SELECT 
    'System Administrator',
    '<EMAIL>',
    'admin123',
    'SystemAdmin'
UNION ALL
SELECT 
    'Approver Level 1',
    '<EMAIL>',
    'approver123',
    'Approver'
UNION ALL
SELECT 
    'Disbursement Officer',
    '<EMAIL>',
    'disburse123',
    'Disbursement';
