using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    [Authorize(requireCompanyUser: true)]
    public class ApplicationController : Controller
    {
        private readonly IApplicationService _applicationService;
        private readonly ILogService _logService;
        
        public ApplicationController(
            IApplicationService applicationService,
            ILogService logService)
        {
            _applicationService = applicationService;
            _logService = logService;
        }
        
        public async Task<IActionResult> Index()
        {
            var company = SessionHelper.GetCompany(HttpContext.Session);
            if (company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var applications = await _applicationService.GetByCompanyIdAsync(company.CompanyID);
            var applicationViewModels = new List<ApplicationViewModel>();
            
            foreach (var app in applications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            return View(applicationViewModels.OrderByDescending(a => a.DateRequested));
        }
        
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }
        
        [HttpPost]
        public async Task<IActionResult> Create(ApplicationViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var user = SessionHelper.GetUser(HttpContext.Session);
            var company = SessionHelper.GetCompany(HttpContext.Session);
            
            if (user == null || company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            try
            {
                var application = await _applicationService.CreateApplicationAsync(model, company.CompanyID);
                
                await _logService.LogAsync(user.UserID, 
                    $"Created new application {application.ApplicationID} for ${application.RequestedCash:N2}");
                
                TempData["SuccessMessage"] = "Application submitted successfully!";
                return RedirectToAction("Details", new { id = application.ApplicationID });
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while submitting the application. Please try again.");
                return View(model);
            }
        }
        
        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var company = SessionHelper.GetCompany(HttpContext.Session);
            if (company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null || application.CompanyID != company.CompanyID)
            {
                return NotFound();
            }
            
            var viewModel = await _applicationService.MapToViewModelAsync(application);
            return View(viewModel);
        }
        
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var company = SessionHelper.GetCompany(HttpContext.Session);
            if (company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null || application.CompanyID != company.CompanyID)
            {
                return NotFound();
            }
            
            // Only allow editing if application is still pending (approval level 0)
            if (application.ApprovalLevel > 0 || application.IsDisbursed || !application.Status)
            {
                TempData["ErrorMessage"] = "This application cannot be edited as it has already been processed.";
                return RedirectToAction("Details", new { id });
            }
            
            var viewModel = await _applicationService.MapToViewModelAsync(application);
            return View(viewModel);
        }
        
        [HttpPost]
        public async Task<IActionResult> Edit(int id, ApplicationViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var company = SessionHelper.GetCompany(HttpContext.Session);
            var user = SessionHelper.GetUser(HttpContext.Session);
            
            if (company == null || user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null || application.CompanyID != company.CompanyID)
            {
                return NotFound();
            }
            
            // Only allow editing if application is still pending
            if (application.ApprovalLevel > 0 || application.IsDisbursed || !application.Status)
            {
                TempData["ErrorMessage"] = "This application cannot be edited as it has already been processed.";
                return RedirectToAction("Details", new { id });
            }
            
            try
            {
                application.Description = model.Description;
                application.Purpose = model.Purpose;
                application.RequestedCash = model.RequestedCash;
                
                // Handle file upload if provided
                if (model.SupportingDocument != null)
                {
                    var filePath = await _applicationService.SaveSupportingDocumentAsync(model.SupportingDocument, id);
                    application.PDFLocation = filePath;
                }
                
                // Update through repository (you'll need to add this method)
                // await _applicationService.UpdateApplicationAsync(application);
                
                await _logService.LogAsync(user.UserID, $"Updated application {id}");
                
                TempData["SuccessMessage"] = "Application updated successfully!";
                return RedirectToAction("Details", new { id });
            }
            catch (Exception)
            {
                ModelState.AddModelError("", "An error occurred while updating the application. Please try again.");
                return View(model);
            }
        }
        
        [HttpGet]
        public async Task<IActionResult> DownloadDocument(int id)
        {
            var company = SessionHelper.GetCompany(HttpContext.Session);
            if (company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null || application.CompanyID != company.CompanyID || string.IsNullOrEmpty(application.PDFLocation))
            {
                return NotFound();
            }
            
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", application.PDFLocation);
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound();
            }
            
            var fileName = Path.GetFileName(filePath);
            var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
            
            return File(fileBytes, "application/pdf", fileName);
        }
    }
}
