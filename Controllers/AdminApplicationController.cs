using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    [Authorize(requireAdminUser: true)]
    public class AdminApplicationController : Controller
    {
        private readonly IApplicationService _applicationService;
        private readonly ILogService _logService;
        
        public AdminApplicationController(
            IApplicationService applicationService,
            ILogService logService)
        {
            _applicationService = applicationService;
            _logService = logService;
        }
        
        public async Task<IActionResult> Index()
        {
            var applications = await _applicationService.GetPendingApplicationsAsync();
            var applicationViewModels = new List<ApplicationViewModel>();
            
            foreach (var app in applications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            return View(applicationViewModels.OrderByDescending(a => a.DateRequested));
        }
        
        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null)
            {
                return NotFound();
            }
            
            var viewModel = await _applicationService.MapToViewModelAsync(application);
            return View(viewModel);
        }
        
        [HttpGet]
        public async Task<IActionResult> Review(int id)
        {
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null)
            {
                return NotFound();
            }
            
            var viewModel = await _applicationService.MapToViewModelAsync(application);
            return View(viewModel);
        }
        
        [HttpPost]
        public async Task<IActionResult> Approve(int id, string comment)
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null)
            {
                return NotFound();
            }
            
            var newApprovalLevel = application.ApprovalLevel + 1;
            var success = await _applicationService.ApproveApplicationAsync(id, newApprovalLevel, comment, user.UserID);
            
            if (success)
            {
                TempData["SuccessMessage"] = $"Application approved to level {newApprovalLevel}";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to approve application";
            }
            
            return RedirectToAction("Details", new { id });
        }
        
        [HttpPost]
        public async Task<IActionResult> Reject(int id, string comment)
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var success = await _applicationService.RejectApplicationAsync(id, comment, user.UserID);
            
            if (success)
            {
                TempData["SuccessMessage"] = "Application rejected";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to reject application";
            }
            
            return RedirectToAction("Details", new { id });
        }
        
        [HttpPost]
        public async Task<IActionResult> Disburse(int id)
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var success = await _applicationService.DisburseApplicationAsync(id, user.Email);
            
            if (success)
            {
                TempData["SuccessMessage"] = "Application disbursed successfully";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to disburse application";
            }
            
            return RedirectToAction("Details", new { id });
        }
        
        [HttpGet]
        public async Task<IActionResult> PendingApprovals()
        {
            var applications = await _applicationService.GetApplicationsByApprovalLevelAsync(0);
            var applicationViewModels = new List<ApplicationViewModel>();
            
            foreach (var app in applications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            return View(applicationViewModels);
        }
        
        [HttpGet]
        public async Task<IActionResult> PendingDisbursements()
        {
            var applications = await _applicationService.GetApplicationsByApprovalLevelAsync(4);
            var applicationViewModels = new List<ApplicationViewModel>();
            
            foreach (var app in applications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            return View(applicationViewModels);
        }
        
        [HttpGet]
        public async Task<IActionResult> DownloadDocument(int id)
        {
            var application = await _applicationService.GetByIdAsync(id);
            if (application == null || string.IsNullOrEmpty(application.PDFLocation))
            {
                return NotFound();
            }
            
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", application.PDFLocation);
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound();
            }
            
            var fileName = Path.GetFileName(filePath);
            var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
            
            return File(fileBytes, "application/pdf", fileName);
        }
    }
}
