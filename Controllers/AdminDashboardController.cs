using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    [Authorize(requireAdminUser: true)]
    public class AdminDashboardController : Controller
    {
        private readonly IApplicationService _applicationService;
        private readonly ICompanyService _companyService;
        
        public AdminDashboardController(
            IApplicationService applicationService,
            ICompanyService companyService)
        {
            _applicationService = applicationService;
            _companyService = companyService;
        }
        
        public async Task<IActionResult> Index()
        {
            var allApplications = await _applicationService.GetPendingApplicationsAsync();
            var allCompanies = await _companyService.GetAllCompaniesAsync();
            
            var applicationViewModels = new List<ApplicationViewModel>();
            foreach (var app in allApplications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            var dashboardModel = new AdminDashboardViewModel
            {
                TotalCompanies = allCompanies.Count(),
                ActiveCompanies = allCompanies.Count(c => c.Status),
                TotalApplications = applicationViewModels.Count,
                PendingApprovals = applicationViewModels.Count(a => a.ApprovalLevel < 4 && a.Status && !a.IsDisbursed),
                PendingDisbursements = applicationViewModels.Count(a => a.ApprovalLevel >= 4 && a.Status && !a.IsDisbursed),
                PendingAcquittals = applicationViewModels.Count(a => a.IsDisbursed),
                TotalRequestedAmount = applicationViewModels.Sum(a => a.RequestedCash),
                TotalDisbursedAmount = applicationViewModels.Where(a => a.IsDisbursed).Sum(a => a.RequestedCash),
                RecentApplications = applicationViewModels.OrderByDescending(a => a.DateRequested).Take(10).ToList(),
                RecentCompanies = allCompanies.OrderByDescending(c => c.CompanyID).Take(5).ToList()
            };
            
            return View(dashboardModel);
        }
    }
}
