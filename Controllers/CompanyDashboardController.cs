using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    [Authorize(requireCompanyUser: true)]
    public class CompanyDashboardController : Controller
    {
        private readonly IApplicationService _applicationService;
        private readonly ICompanyService _companyService;
        
        public CompanyDashboardController(
            IApplicationService applicationService,
            ICompanyService companyService)
        {
            _applicationService = applicationService;
            _companyService = companyService;
        }
        
        public async Task<IActionResult> Index()
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            var company = SessionHelper.GetCompany(HttpContext.Session);
            
            if (user == null || company == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var applications = await _applicationService.GetByCompanyIdAsync(company.CompanyID);
            var applicationViewModels = new List<ApplicationViewModel>();
            
            foreach (var app in applications)
            {
                applicationViewModels.Add(await _applicationService.MapToViewModelAsync(app));
            }
            
            var dashboardModel = new CompanyDashboardViewModel
            {
                CompanyName = company.Name,
                TotalApplications = applicationViewModels.Count,
                PendingApplications = applicationViewModels.Count(a => a.ApprovalLevel < 4 && a.Status && !a.IsDisbursed),
                ApprovedApplications = applicationViewModels.Count(a => a.ApprovalLevel >= 4 && a.Status),
                DisbursedApplications = applicationViewModels.Count(a => a.IsDisbursed),
                TotalRequestedAmount = applicationViewModels.Sum(a => a.RequestedCash),
                TotalDisbursedAmount = applicationViewModels.Where(a => a.IsDisbursed).Sum(a => a.RequestedCash),
                RecentApplications = applicationViewModels.OrderByDescending(a => a.DateRequested).Take(5).ToList(),
                PendingAcquittals = applicationViewModels.Where(a => a.IsDisbursed).Take(5).ToList()
            };
            
            return View(dashboardModel);
        }
    }
}
