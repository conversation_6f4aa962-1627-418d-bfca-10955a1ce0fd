using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.Entities;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    [Authorize(requireAdminUser: true)]
    public class AdminCompanyController : Controller
    {
        private readonly ICompanyService _companyService;
        private readonly ILogService _logService;
        
        public AdminCompanyController(
            ICompanyService companyService,
            ILogService logService)
        {
            _companyService = companyService;
            _logService = logService;
        }
        
        public async Task<IActionResult> Index()
        {
            var companies = await _companyService.GetAllCompaniesAsync();
            return View(companies.OrderBy(c => c.Name));
        }
        
        [HttpGet]
        public async Task<IActionResult> Details(int id)
        {
            var company = await _companyService.GetByIdAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            
            return View(company);
        }
        
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }
        
        [HttpPost]
        public async Task<IActionResult> Create(CompanyRegistrationViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var (success, company, adminUser) = await _companyService.RegisterCompanyWithAdminAsync(model);
            
            if (!success)
            {
                ModelState.AddModelError("", "Registration failed. Email may already be in use.");
                return View(model);
            }
            
            await _logService.LogAsync(user.UserID, $"Created new company: {company?.Name}");
            TempData["SuccessMessage"] = "Company created successfully!";
            return RedirectToAction("Details", new { id = company?.CompanyID });
        }
        
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var company = await _companyService.GetByIdAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            
            return View(company);
        }
        
        [HttpPost]
        public async Task<IActionResult> Edit(int id, Company model)
        {
            if (id != model.CompanyID)
            {
                return NotFound();
            }
            
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            try
            {
                await _companyService.UpdateCompanyAsync(model);
                await _logService.LogAsync(user.UserID, $"Updated company: {model.Name}");
                TempData["SuccessMessage"] = "Company updated successfully!";
                return RedirectToAction("Details", new { id });
            }
            catch (Exception)
            {
                ModelState.AddModelError("", "An error occurred while updating the company.");
                return View(model);
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var company = await _companyService.GetByIdAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            
            company.Status = !company.Status;
            await _companyService.UpdateCompanyAsync(company);
            
            var statusText = company.Status ? "activated" : "deactivated";
            await _logService.LogAsync(user.UserID, $"Company {company.Name} {statusText}");
            
            TempData["SuccessMessage"] = $"Company {statusText} successfully!";
            return RedirectToAction("Details", new { id });
        }
        
        [HttpGet]
        public async Task<IActionResult> Delete(int id)
        {
            var company = await _companyService.GetByIdAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            
            return View(company);
        }
        
        [HttpPost, ActionName("Delete")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user == null)
            {
                return RedirectToAction("Login", "Auth");
            }
            
            var company = await _companyService.GetByIdAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            
            try
            {
                await _companyService.DeleteCompanyAsync(id);
                await _logService.LogAsync(user.UserID, $"Deleted company: {company.Name}");
                TempData["SuccessMessage"] = "Company deleted successfully!";
                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                TempData["ErrorMessage"] = "Cannot delete company. It may have associated applications.";
                return RedirectToAction("Details", new { id });
            }
        }
    }
}
