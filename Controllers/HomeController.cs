using System.Diagnostics;
using _CashDisbursement.Models;
using _CashDisbursement.Utilities;
using Microsoft.AspNetCore.Mvc;

namespace _CashDisbursement.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            // Redirect authenticated users to their appropriate dashboard
            if (SessionHelper.IsAuthenticated(HttpContext.Session))
            {
                var user = SessionHelper.GetUser(HttpContext.Session);
                if (user != null)
                {
                    if (SessionHelper.IsCompanyUser(HttpContext.Session))
                    {
                        return RedirectToAction("Index", "CompanyDashboard");
                    }
                    else if (SessionHelper.IsAdminUser(HttpContext.Session))
                    {
                        return RedirectToAction("Index", "AdminDashboard");
                    }
                }
            }

            // Redirect unauthenticated users to login
            return RedirectToAction("Login", "Auth");
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
