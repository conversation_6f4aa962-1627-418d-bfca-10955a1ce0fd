using Microsoft.AspNetCore.Mvc;
using _CashDisbursement.Models.ViewModels;
using _CashDisbursement.Services;
using _CashDisbursement.Utilities;

namespace _CashDisbursement.Controllers
{
    public class AuthController : Controller
    {
        private readonly IAuthenticationService _authService;
        private readonly ICompanyService _companyService;
        private readonly ILogService _logService;
        
        public AuthController(
            IAuthenticationService authService,
            ICompanyService companyService,
            ILogService logService)
        {
            _authService = authService;
            _companyService = companyService;
            _logService = logService;
        }
        
        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            if (SessionHelper.IsAuthenticated(HttpContext.Session))
            {
                var user = SessionHelper.GetUser(HttpContext.Session);
                if (user != null)
                {
                    return RedirectToAppropriatePortal(user);
                }
            }
            
            ViewData["ReturnUrl"] = returnUrl;
            return View();
        }
        
        [HttpPost]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var user = await _authService.AuthenticateAsync(model.Email, model.Password);
            if (user == null)
            {
                ModelState.AddModelError("", "Invalid email or password.");
                return View(model);
            }
            
            // Set user session
            SessionHelper.SetUser(HttpContext.Session, user);
            
            // Set company session if user belongs to a company
            if (user.CompanyID.HasValue)
            {
                var company = await _companyService.GetByIdAsync(user.CompanyID.Value);
                if (company != null)
                {
                    SessionHelper.SetCompany(HttpContext.Session, company);
                }
            }
            
            // Log the login
            await _logService.LogAsync(user.UserID, "User logged in");
            
            // Redirect to appropriate portal or return URL
            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }
            
            return RedirectToAppropriatePortal(user);
        }
        
        [HttpGet]
        public IActionResult Register()
        {
            return View();
        }
        
        [HttpPost]
        public async Task<IActionResult> Register(CompanyRegistrationViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }
            
            var (success, company, adminUser) = await _companyService.RegisterCompanyWithAdminAsync(model);
            
            if (!success)
            {
                ModelState.AddModelError("", "Registration failed. Email may already be in use.");
                return View(model);
            }
            
            TempData["SuccessMessage"] = "Company registered successfully! You can now log in.";
            return RedirectToAction("Login");
        }
        
        [HttpPost]
        public async Task<IActionResult> Logout()
        {
            var user = SessionHelper.GetUser(HttpContext.Session);
            if (user != null)
            {
                await _logService.LogAsync(user.UserID, "User logged out");
            }
            
            SessionHelper.ClearSession(HttpContext.Session);
            return RedirectToAction("Login");
        }
        
        private IActionResult RedirectToAppropriatePortal(Models.Entities.User user)
        {
            return user.Role switch
            {
                Models.Entities.UserRoles.CompanyAdmin or Models.Entities.UserRoles.CompanyUser => 
                    RedirectToAction("Index", "CompanyDashboard"),
                Models.Entities.UserRoles.SystemAdmin or Models.Entities.UserRoles.Approver or Models.Entities.UserRoles.Disbursement => 
                    RedirectToAction("Index", "AdminDashboard"),
                _ => RedirectToAction("Login")
            };
        }
    }
}
