@model _CashDisbursement.Models.ViewModels.CompanyDashboardViewModel
@{
    ViewData["Title"] = "Company Dashboard";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Welcome, @Model.CompanyName</h1>
                <p class="text-muted mb-0">Manage your fund applications and track their progress</p>
            </div>
            <a asp-controller="Application" asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>New Application
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-primary mb-2">
                    <i class="bi bi-file-earmark-text" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.TotalApplications</h3>
                <p class="text-muted mb-0">Total Applications</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-warning mb-2">
                    <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.PendingApplications</h3>
                <p class="text-muted mb-0">Pending Review</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-success mb-2">
                    <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.ApprovedApplications</h3>
                <p class="text-muted mb-0">Approved</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-info mb-2">
                    <i class="bi bi-cash-coin" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.DisbursedApplications</h3>
                <p class="text-muted mb-0">Disbursed</p>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h5 class="card-title text-primary">
                    <i class="bi bi-graph-up me-2"></i>Total Requested
                </h5>
                <h2 class="text-primary mb-0">${@Model.TotalRequestedAmount:N2}</h2>
                <small class="text-muted">Across all applications</small>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h5 class="card-title text-success">
                    <i class="bi bi-cash-stack me-2"></i>Total Disbursed
                </h5>
                <h2 class="text-success mb-0">${@Model.TotalDisbursedAmount:N2}</h2>
                <small class="text-muted">Successfully received</small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Applications -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent Applications
                    </h5>
                    <a asp-controller="Application" asp-action="Index" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.RecentApplications.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var app in Model.RecentApplications)
                                {
                                    <tr>
                                        <td><span class="badge bg-light text-dark">#@app.ApplicationID</span></td>
                                        <td>
                                            <div class="fw-medium">@app.Description</div>
                                            <small class="text-muted">@app.Purpose</small>
                                        </td>
                                        <td class="fw-medium">${@app.RequestedCash:N2}</td>
                                        <td>
                                            @if (app.IsDisbursed)
                                            {
                                                <span class="badge bg-success">Disbursed</span>
                                            }
                                            else if (!app.Status)
                                            {
                                                <span class="badge bg-danger">Rejected</span>
                                            }
                                            else if (app.ApprovalLevel >= 4)
                                            {
                                                <span class="badge bg-info">Approved</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">Pending</span>
                                            }
                                        </td>
                                        <td>
                                            <small>@app.DateRequested.ToString("MMM dd, yyyy")</small>
                                        </td>
                                        <td>
                                            <a asp-controller="Application" asp-action="Details" asp-route-id="@app.ApplicationID" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No applications yet</p>
                        <a asp-controller="Application" asp-action="Create" class="btn btn-primary">
                            Create Your First Application
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Pending Acquittals -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-check me-2"></i>Pending Acquittals
                </h5>
            </div>
            <div class="card-body">
                @if (Model.PendingAcquittals.Any())
                {
                    @foreach (var app in Model.PendingAcquittals)
                    {
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                            <div>
                                <div class="fw-medium">#@app.ApplicationID</div>
                                <small class="text-muted">${@app.RequestedCash:N2}</small>
                            </div>
                            <button class="btn btn-outline-primary btn-sm">
                                Submit
                            </button>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">No pending acquittals</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
