@model IEnumerable<_CashDisbursement.Models.ViewModels.ApplicationViewModel>
@{
    ViewData["Title"] = "My Applications";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">My Applications</h1>
                <p class="text-muted mb-0">Track and manage your fund applications</p>
            </div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>New Application
            </a>
        </div>
    </div>
</div>

@if (Model.Any())
{
    <div class="row">
        @foreach (var app in Model)
        {
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-light text-dark">#@app.ApplicationID</span>
                            @if (app.IsDisbursed)
                            {
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>Disbursed
                                </span>
                            }
                            else if (!app.Status)
                            {
                                <span class="badge bg-danger">
                                    <i class="bi bi-x-circle me-1"></i>Rejected
                                </span>
                            }
                            else if (app.ApprovalLevel >= 4)
                            {
                                <span class="badge bg-info">
                                    <i class="bi bi-clock me-1"></i>Approved
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-warning">
                                    <i class="bi bi-hourglass-split me-1"></i>Pending
                                </span>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">@app.Description</h5>
                        <p class="card-text text-muted">@app.Purpose</p>
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-primary mb-1">${@app.RequestedCash:N2}</h6>
                                    <small class="text-muted">Requested</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">Level @app.ApprovalLevel</h6>
                                <small class="text-muted">Approval</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                Submitted: @app.DateRequested.ToString("MMM dd, yyyy")
                            </small>
                            @if (app.DateDisbursed.HasValue)
                            {
                                <br>
                                <small class="text-success">
                                    <i class="bi bi-cash me-1"></i>
                                    Disbursed: @app.DateDisbursed.Value.ToString("MMM dd, yyyy")
                                </small>
                            }
                        </div>

                        @if (!string.IsNullOrEmpty(app.ApprovalComment))
                        {
                            <div class="alert alert-light py-2 mb-3">
                                <small>
                                    <strong>Comment:</strong> @app.ApprovalComment
                                </small>
                            </div>
                        }
                    </div>
                    <div class="card-footer bg-white border-0 py-3">
                        <div class="d-flex justify-content-between">
                            <a asp-action="Details" asp-route-id="@app.ApplicationID" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View Details
                            </a>
                            @if (app.ApprovalLevel == 0 && app.Status && !app.IsDisbursed)
                            {
                                <a asp-action="Edit" asp-route-id="@app.ApplicationID" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-pencil me-1"></i>Edit
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(app.PDFLocation))
                            {
                                <a asp-action="DownloadDocument" asp-route-id="@app.ApplicationID" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-download me-1"></i>Document
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-file-earmark-text text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 mb-2">No Applications Yet</h4>
                    <p class="text-muted mb-4">You haven't submitted any fund applications yet.</p>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Create Your First Application
                    </a>
                </div>
            </div>
        </div>
    </div>
}
