@model _CashDisbursement.Models.ViewModels.ApplicationViewModel
@{
    ViewData["Title"] = $"Edit Application #{Model.ApplicationID}";
}

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow">
            <div class="card-header bg-warning text-white py-3">
                <h4 class="mb-0">
                    <i class="bi bi-pencil-square me-2"></i>Edit Application #@Model.ApplicationID
                </h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Note:</strong> You can only edit applications that haven't been reviewed yet.
                </div>

                <form asp-action="Edit" method="post" enctype="multipart/form-data">
                    <input type="hidden" asp-for="ApplicationID" />
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="form-floating mb-3">
                                <textarea asp-for="Description" class="form-control" placeholder="Application Description" style="height: 120px;"></textarea>
                                <label asp-for="Description">Application Description</label>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">Provide a detailed description of your funding request</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-floating mb-3">
                                <textarea asp-for="Purpose" class="form-control" placeholder="Purpose of Funding" style="height: 120px;"></textarea>
                                <label asp-for="Purpose">Purpose of Funding</label>
                                <span asp-validation-for="Purpose" class="text-danger"></span>
                                <div class="form-text">Explain how the funds will be used</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="RequestedCash" class="form-control" placeholder="0.00" step="0.01" />
                                <label asp-for="RequestedCash">Requested Amount ($)</label>
                                <span asp-validation-for="RequestedCash" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="SupportingDocument" class="form-label">Update Supporting Document (PDF)</label>
                                <input asp-for="SupportingDocument" class="form-control" type="file" accept=".pdf" />
                                <span asp-validation-for="SupportingDocument" class="text-danger"></span>
                                <div class="form-text">
                                    @if (!string.IsNullOrEmpty(Model.PDFLocation))
                                    {
                                        <span class="text-success">
                                            <i class="bi bi-file-earmark-pdf me-1"></i>Current document available
                                        </span>
                                    }
                                    else
                                    {
                                        <span>Upload a new supporting document (optional)</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.PDFLocation))
                    {
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="alert alert-light" role="alert">
                                    <i class="bi bi-file-earmark-pdf me-2"></i>
                                    <strong>Current Document:</strong>
                                    <a asp-action="DownloadDocument" asp-route-id="@Model.ApplicationID" class="btn btn-outline-primary btn-sm ms-2">
                                        <i class="bi bi-download me-1"></i>Download Current PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> Once you save changes, the application will remain in the review queue. 
                        Make sure all information is accurate before submitting.
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Details" asp-route-id="@Model.ApplicationID" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-warning px-4">
                            <i class="bi bi-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Format currency input
        document.getElementById('RequestedCash').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d.]/g, '');
            if (value) {
                e.target.value = parseFloat(value).toFixed(2);
            }
        });

        // File upload validation
        document.getElementById('SupportingDocument').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.type !== 'application/pdf') {
                    alert('Please select a PDF file only.');
                    e.target.value = '';
                    return;
                }
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    alert('File size must be less than 10MB.');
                    e.target.value = '';
                    return;
                }
            }
        });
    </script>
}
