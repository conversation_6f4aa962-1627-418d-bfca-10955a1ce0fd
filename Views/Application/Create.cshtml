@model _CashDisbursement.Models.ViewModels.ApplicationViewModel
@{
    ViewData["Title"] = "New Fund Application";
}

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow">
            <div class="card-header bg-primary text-white py-3">
                <h4 class="mb-0">
                    <i class="bi bi-plus-circle me-2"></i>New Fund Application
                </h4>
            </div>
            <div class="card-body p-4">
                <form asp-action="Create" method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-floating mb-3">
                                <textarea asp-for="Description" class="form-control" placeholder="Application Description" style="height: 120px;"></textarea>
                                <label asp-for="Description">Application Description</label>
                                <span asp-validation-for="Description" class="text-danger"></span>
                                <div class="form-text">Provide a detailed description of your funding request</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-floating mb-3">
                                <textarea asp-for="Purpose" class="form-control" placeholder="Purpose of Funding" style="height: 120px;"></textarea>
                                <label asp-for="Purpose">Purpose of Funding</label>
                                <span asp-validation-for="Purpose" class="text-danger"></span>
                                <div class="form-text">Explain how the funds will be used</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input asp-for="RequestedCash" class="form-control" placeholder="0.00" step="0.01" />
                                <label asp-for="RequestedCash">Requested Amount ($)</label>
                                <span asp-validation-for="RequestedCash" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="SupportingDocument" class="form-label">Supporting Document (PDF)</label>
                                <input asp-for="SupportingDocument" class="form-control" type="file" accept=".pdf" />
                                <span asp-validation-for="SupportingDocument" class="text-danger"></span>
                                <div class="form-text">Upload any supporting documentation (optional)</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Application Process:</strong> Your application will go through a multi-level approval process. 
                        You will receive email notifications at each stage of the review.
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-controller="CompanyDashboard" asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="bi bi-send me-2"></i>Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Format currency input
        document.getElementById('RequestedCash').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d.]/g, '');
            if (value) {
                e.target.value = parseFloat(value).toFixed(2);
            }
        });

        // File upload validation
        document.getElementById('SupportingDocument').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.type !== 'application/pdf') {
                    alert('Please select a PDF file only.');
                    e.target.value = '';
                    return;
                }
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    alert('File size must be less than 10MB.');
                    e.target.value = '';
                    return;
                }
            }
        });
    </script>
}
