@model IEnumerable<_CashDisbursement.Models.Entities.Company>
@{
    ViewData["Title"] = "Company Management";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Company Management</h1>
                <p class="text-muted mb-0">Manage registered companies and their status</p>
            </div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Add New Company
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-primary mb-2">
                    <i class="bi bi-building" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.Count()</h3>
                <p class="text-muted mb-0">Total Companies</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-success mb-2">
                    <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.Count(c => c.Status)</h3>
                <p class="text-muted mb-0">Active Companies</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-warning mb-2">
                    <i class="bi bi-pause-circle" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.Count(c => !c.Status)</h3>
                <p class="text-muted mb-0">Inactive Companies</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="text-info mb-2">
                    <i class="bi bi-calendar-plus" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.Count(c => c.CompanyID > (Model.Max(x => x.CompanyID) - 5))</h3>
                <p class="text-muted mb-0">Recent Registrations</p>
            </div>
        </div>
    </div>
</div>

@if (Model.Any())
{
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0 py-3">
            <h5 class="mb-0">
                <i class="bi bi-list me-2"></i>All Companies
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Company Name</th>
                            <th>Email</th>
                            <th>Contact</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var company in Model)
                        {
                            <tr>
                                <td>
                                    <span class="badge bg-light text-dark">#@company.CompanyID</span>
                                </td>
                                <td>
                                    <div class="fw-medium">@company.Name</div>
                                    @if (!string.IsNullOrEmpty(company.Address))
                                    {
                                        <small class="text-muted">@company.Address</small>
                                    }
                                </td>
                                <td>
                                    <a href="mailto:@company.Email" class="text-decoration-none">
                                        @company.Email
                                    </a>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(company.Contact))
                                    {
                                        <span>@company.Contact</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (company.Status)
                                    {
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-pause-circle me-1"></i>Inactive
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@company.CompanyID" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@company.CompanyID" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form asp-action="ToggleStatus" asp-route-id="@company.CompanyID" 
                                              method="post" class="d-inline">
                                            @if (company.Status)
                                            {
                                                <button type="submit" class="btn btn-outline-warning btn-sm" 
                                                        onclick="return confirm('Are you sure you want to deactivate this company?')"
                                                        title="Deactivate">
                                                    <i class="bi bi-pause"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <button type="submit" class="btn btn-outline-success btn-sm" 
                                                        onclick="return confirm('Are you sure you want to activate this company?')"
                                                        title="Activate">
                                                    <i class="bi bi-play"></i>
                                                </button>
                                            }
                                        </form>
                                        <a asp-action="Delete" asp-route-id="@company.CompanyID" 
                                           class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}
else
{
    <div class="card border-0 shadow-sm">
        <div class="card-body text-center py-5">
            <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
            <h4 class="mt-3 mb-2">No Companies Registered</h4>
            <p class="text-muted mb-4">There are no companies registered in the system yet.</p>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Add First Company
            </a>
        </div>
    </div>
}

@section Scripts {
    <script>
        // Add confirmation for status toggle
        document.addEventListener('DOMContentLoaded', function() {
            const toggleForms = document.querySelectorAll('form[action*="ToggleStatus"]');
            toggleForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const button = this.querySelector('button');
                    const isActivating = button.classList.contains('btn-outline-success');
                    const action = isActivating ? 'activate' : 'deactivate';
                    
                    if (!confirm(`Are you sure you want to ${action} this company?`)) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
}
