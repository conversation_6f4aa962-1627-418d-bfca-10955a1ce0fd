@model _CashDisbursement.Models.ViewModels.ApplicationViewModel
@{
    ViewData["Title"] = $"Review Application #{Model.ApplicationID}";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Review Application #@Model.ApplicationID</h1>
                <p class="text-muted mb-0">@Model.CompanyName - Current Level: @Model.ApprovalLevel</p>
            </div>
            <a asp-action="Details" asp-route-id="@Model.ApplicationID" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Details
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Application Summary -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-primary text-white py-3">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>Application Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Company:</strong></div>
                    <div class="col-sm-9">@Model.CompanyName</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Amount:</strong></div>
                    <div class="col-sm-9"><span class="h5 text-success">${@Model.RequestedCash:N2}</span></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Description:</strong></div>
                    <div class="col-sm-9">@Model.Description</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Purpose:</strong></div>
                    <div class="col-sm-9">@Model.Purpose</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Submitted:</strong></div>
                    <div class="col-sm-9">@Model.DateRequested.ToString("MMMM dd, yyyy")</div>
                </div>
                @if (!string.IsNullOrEmpty(Model.PDFLocation))
                {
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Document:</strong></div>
                        <div class="col-sm-9">
                            <a asp-action="DownloadDocument" asp-route-id="@Model.ApplicationID" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download me-2"></i>Download Supporting PDF
                            </a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Review Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-warning text-white py-3">
                <h5 class="mb-0">
                    <i class="bi bi-clipboard-check me-2"></i>Review Actions
                </h5>
            </div>
            <div class="card-body">
                <!-- Approve Form -->
                <form asp-action="Approve" asp-route-id="@Model.ApplicationID" method="post" class="mb-3">
                    <div class="mb-3">
                        <label for="approveComment" class="form-label">Approval Comment</label>
                        <textarea name="comment" id="approveComment" class="form-control" rows="3" 
                                  placeholder="Enter approval comments (optional)"></textarea>
                    </div>
                    <button type="submit" class="btn btn-success w-100" onclick="return confirm('Are you sure you want to approve this application?')">
                        <i class="bi bi-check-circle me-2"></i>Approve Application
                    </button>
                </form>

                <hr>

                <!-- Reject Form -->
                <form asp-action="Reject" asp-route-id="@Model.ApplicationID" method="post">
                    <div class="mb-3">
                        <label for="rejectComment" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea name="comment" id="rejectComment" class="form-control" rows="3" 
                                  placeholder="Enter reason for rejection" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to reject this application? This action cannot be undone.')">
                        <i class="bi bi-x-circle me-2"></i>Reject Application
                    </button>
                </form>
            </div>
        </div>

        <!-- Current Progress -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0">
                    <i class="bi bi-list-check me-2"></i>Approval Progress
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @for (int level = 0; level <= 4; level++)
                    {
                        <div class="timeline-item @(Model.ApprovalLevel >= level ? "completed" : "") @(Model.ApprovalLevel == level ? "current" : "")">
                            <div class="timeline-marker">
                                @if (Model.ApprovalLevel > level)
                                {
                                    <i class="bi bi-check-circle text-success"></i>
                                }
                                else if (Model.ApprovalLevel == level)
                                {
                                    <i class="bi bi-arrow-right text-warning"></i>
                                }
                                else
                                {
                                    <i class="bi bi-circle text-muted"></i>
                                }
                            </div>
                            <div class="timeline-content">
                                @switch (level)
                                {
                                    case 0:
                                        <strong>Submitted</strong>
                                        <small class="text-muted d-block">Application received</small>
                                        break;
                                    case 1:
                                        <strong>Level 1 Review</strong>
                                        <small class="text-muted d-block">Initial review</small>
                                        break;
                                    case 2:
                                        <strong>Level 2 Review</strong>
                                        <small class="text-muted d-block">Secondary review</small>
                                        break;
                                    case 3:
                                        <strong>Level 3 Review</strong>
                                        <small class="text-muted d-block">Final review</small>
                                        break;
                                    case 4:
                                        <strong>Approved</strong>
                                        <small class="text-muted d-block">Ready for disbursement</small>
                                        break;
                                }
                                @if (Model.ApprovalLevel == level)
                                {
                                    <span class="badge bg-warning text-dark">Current Step</span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .timeline-item.current .timeline-content {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
        }
    </style>
}
