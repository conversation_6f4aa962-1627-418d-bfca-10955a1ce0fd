@model IEnumerable<_CashDisbursement.Models.ViewModels.ApplicationViewModel>
@{
    ViewData["Title"] = "Application Management";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Application Management</h1>
                <p class="text-muted mb-0">Review and manage fund applications</p>
            </div>
            <div class="d-flex gap-2">
                <a asp-action="PendingApprovals" class="btn btn-outline-warning">
                    <i class="bi bi-clock me-2"></i>Pending Approvals
                </a>
                <a asp-action="PendingDisbursements" class="btn btn-outline-info">
                    <i class="bi bi-cash-coin me-2"></i>Ready for Disbursement
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filter Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <ul class="nav nav-pills" id="applicationTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                    All Applications
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pending-tab" data-bs-toggle="pill" data-bs-target="#pending" type="button" role="tab">
                    Pending Review
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="approved-tab" data-bs-toggle="pill" data-bs-target="#approved" type="button" role="tab">
                    Approved
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="disbursed-tab" data-bs-toggle="pill" data-bs-target="#disbursed" type="button" role="tab">
                    Disbursed
                </button>
            </li>
        </ul>
    </div>
</div>

@if (Model.Any())
{
    <div class="row">
        @foreach (var app in Model)
        {
            <div class="col-lg-6 col-xl-4 mb-4" data-status="@(app.IsDisbursed ? "disbursed" : (app.ApprovalLevel >= 4 ? "approved" : "pending"))">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-light text-dark">#@app.ApplicationID</span>
                            @if (app.IsDisbursed)
                            {
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>Disbursed
                                </span>
                            }
                            else if (!app.Status)
                            {
                                <span class="badge bg-danger">
                                    <i class="bi bi-x-circle me-1"></i>Rejected
                                </span>
                            }
                            else if (app.ApprovalLevel >= 4)
                            {
                                <span class="badge bg-info">
                                    <i class="bi bi-cash-coin me-1"></i>Ready for Disbursement
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-warning">
                                    <i class="bi bi-hourglass-split me-1"></i>Level @app.ApprovalLevel
                                </span>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title text-primary">@app.CompanyName</h6>
                        <h5 class="card-title">@app.Description</h5>
                        <p class="card-text text-muted small">@app.Purpose</p>
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="text-primary mb-1">${@app.RequestedCash:N2}</h6>
                                    <small class="text-muted">Requested</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted mb-1">Level @app.ApprovalLevel</h6>
                                <small class="text-muted">Approval</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                Submitted: @app.DateRequested.ToString("MMM dd, yyyy")
                            </small>
                            @if (app.DateDisbursed.HasValue)
                            {
                                <br>
                                <small class="text-success">
                                    <i class="bi bi-cash me-1"></i>
                                    Disbursed: @app.DateDisbursed.Value.ToString("MMM dd, yyyy")
                                </small>
                            }
                        </div>

                        @if (!string.IsNullOrEmpty(app.ApprovalComment))
                        {
                            <div class="alert alert-light py-2 mb-3">
                                <small>
                                    <strong>Comment:</strong> @app.ApprovalComment
                                </small>
                            </div>
                        }
                    </div>
                    <div class="card-footer bg-white border-0 py-3">
                        <div class="d-flex justify-content-between">
                            <a asp-action="Details" asp-route-id="@app.ApplicationID" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>View
                            </a>
                            @if (app.Status && !app.IsDisbursed)
                            {
                                @if (app.ApprovalLevel < 4)
                                {
                                    <a asp-action="Review" asp-route-id="@app.ApplicationID" class="btn btn-warning btn-sm">
                                        <i class="bi bi-clipboard-check me-1"></i>Review
                                    </a>
                                }
                                else
                                {
                                    <form asp-action="Disburse" asp-route-id="@app.ApplicationID" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('Are you sure you want to disburse this application?')">
                                            <i class="bi bi-cash-coin me-1"></i>Disburse
                                        </button>
                                    </form>
                                }
                            }
                            @if (!string.IsNullOrEmpty(app.PDFLocation))
                            {
                                <a asp-action="DownloadDocument" asp-route-id="@app.ApplicationID" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-download me-1"></i>PDF
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-file-earmark-text text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 mb-2">No Applications Found</h4>
                    <p class="text-muted mb-0">There are no applications to review at this time.</p>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        // Filter applications by status
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('#applicationTabs button');
            const applications = document.querySelectorAll('[data-status]');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.id.replace('-tab', '');
                    
                    applications.forEach(app => {
                        const status = app.getAttribute('data-status');
                        
                        if (filter === 'all') {
                            app.style.display = 'block';
                        } else if (filter === status) {
                            app.style.display = 'block';
                        } else {
                            app.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
}
