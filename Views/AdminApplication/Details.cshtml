@model _CashDisbursement.Models.ViewModels.ApplicationViewModel
@{
    ViewData["Title"] = $"Application #{Model.ApplicationID} - Admin Review";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Application #@Model.ApplicationID</h1>
                <p class="text-muted mb-0">@Model.CompanyName - @Model.StatusText</p>
            </div>
            <div class="d-flex gap-2">
                @if (Model.Status && !Model.IsDisbursed && Model.ApprovalLevel < 4)
                {
                    <a asp-action="Review" asp-route-id="@Model.ApplicationID" class="btn btn-warning">
                        <i class="bi bi-clipboard-check me-2"></i>Review Application
                    </a>
                }
                @if (Model.Status && !Model.IsDisbursed && Model.ApprovalLevel >= 4)
                {
                    <form asp-action="Disburse" asp-route-id="@Model.ApplicationID" method="post" class="d-inline">
                        <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to disburse this application?')">
                            <i class="bi bi-cash-coin me-2"></i>Disburse Funds
                        </button>
                    </form>
                }
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Back to List
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Application Details -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>Application Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Company:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="h6 text-primary">@Model.CompanyName</span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Description:</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.Description
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Purpose:</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.Purpose
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Requested Amount:</strong>
                    </div>
                    <div class="col-sm-9">
                        <span class="h4 text-success">${@Model.RequestedCash:N2}</span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Date Submitted:</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.DateRequested.ToString("MMMM dd, yyyy 'at' hh:mm tt")
                    </div>
                </div>
                @if (Model.DateDisbursed.HasValue)
                {
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Date Disbursed:</strong>
                        </div>
                        <div class="col-sm-9">
                            <span class="text-success">
                                @Model.DateDisbursed.Value.ToString("MMMM dd, yyyy 'at' hh:mm tt")
                            </span>
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(Model.DisbursedBy))
                {
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Disbursed By:</strong>
                        </div>
                        <div class="col-sm-9">
                            @Model.DisbursedBy
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(Model.ApprovalComment))
                {
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Comments:</strong>
                        </div>
                        <div class="col-sm-9">
                            <div class="alert alert-info py-2">
                                @Model.ApprovalComment
                            </div>
                        </div>
                    </div>
                }
                @if (!string.IsNullOrEmpty(Model.PDFLocation))
                {
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>Supporting Document:</strong>
                        </div>
                        <div class="col-sm-9">
                            <a asp-action="DownloadDocument" asp-route-id="@Model.ApplicationID" class="btn btn-outline-primary">
                                <i class="bi bi-download me-2"></i>Download PDF
                            </a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Status Card -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>Current Status
                </h5>
            </div>
            <div class="card-body text-center">
                @if (Model.IsDisbursed)
                {
                    <div class="text-success mb-2">
                        <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-success">Disbursed</h5>
                    <p class="text-muted mb-0">Funds have been successfully disbursed</p>
                }
                else if (!Model.Status)
                {
                    <div class="text-danger mb-2">
                        <i class="bi bi-x-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-danger">Rejected</h5>
                    <p class="text-muted mb-0">Application has been rejected</p>
                }
                else if (Model.ApprovalLevel >= 4)
                {
                    <div class="text-info mb-2">
                        <i class="bi bi-cash-coin" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-info">Ready for Disbursement</h5>
                    <p class="text-muted mb-0">All approvals completed</p>
                }
                else
                {
                    <div class="text-warning mb-2">
                        <i class="bi bi-hourglass-split" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-warning">Under Review</h5>
                    <p class="text-muted mb-0">Currently at approval level @Model.ApprovalLevel</p>
                }
            </div>
        </div>

        <!-- Approval Progress -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <h5 class="mb-0">
                    <i class="bi bi-list-check me-2"></i>Approval Progress
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @for (int level = 0; level <= 4; level++)
                    {
                        <div class="timeline-item @(Model.ApprovalLevel >= level ? "completed" : "")">
                            <div class="timeline-marker">
                                @if (Model.ApprovalLevel > level)
                                {
                                    <i class="bi bi-check-circle text-success"></i>
                                }
                                else if (Model.ApprovalLevel == level)
                                {
                                    <i class="bi bi-clock text-warning"></i>
                                }
                                else
                                {
                                    <i class="bi bi-circle text-muted"></i>
                                }
                            </div>
                            <div class="timeline-content">
                                @switch (level)
                                {
                                    case 0:
                                        <strong>Submitted</strong>
                                        <small class="text-muted d-block">Application received</small>
                                        break;
                                    case 1:
                                        <strong>Level 1 Review</strong>
                                        <small class="text-muted d-block">Initial review</small>
                                        break;
                                    case 2:
                                        <strong>Level 2 Review</strong>
                                        <small class="text-muted d-block">Secondary review</small>
                                        break;
                                    case 3:
                                        <strong>Level 3 Review</strong>
                                        <small class="text-muted d-block">Final review</small>
                                        break;
                                    case 4:
                                        <strong>Approved</strong>
                                        <small class="text-muted d-block">Ready for disbursement</small>
                                        break;
                                }
                            </div>
                        </div>
                    }
                    @if (Model.IsDisbursed)
                    {
                        <div class="timeline-item completed">
                            <div class="timeline-marker">
                                <i class="bi bi-check-circle text-success"></i>
                            </div>
                            <div class="timeline-content">
                                <strong>Disbursed</strong>
                                <small class="text-muted d-block">Funds transferred</small>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
