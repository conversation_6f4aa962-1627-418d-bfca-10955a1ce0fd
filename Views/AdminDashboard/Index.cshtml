@model _CashDisbursement.Models.ViewModels.AdminDashboardViewModel
@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">Admin Dashboard</h1>
                <p class="text-muted mb-0">Monitor and manage the cash disbursement system</p>
            </div>
            <div class="d-flex gap-2">
                <a asp-controller="AdminApplication" asp-action="Index" class="btn btn-outline-primary">
                    <i class="bi bi-file-earmark-check me-2"></i>Review Applications
                </a>
                <a asp-controller="AdminCompany" asp-action="Index" class="btn btn-primary">
                    <i class="bi bi-building me-2"></i>Manage Companies
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-primary mb-2">
                    <i class="bi bi-building" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.TotalCompanies</h3>
                <p class="text-muted mb-0">Total Companies</p>
                <small class="text-success">@Model.ActiveCompanies active</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-warning mb-2">
                    <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.PendingApprovals</h3>
                <p class="text-muted mb-0">Pending Approvals</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-info mb-2">
                    <i class="bi bi-cash-coin" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.PendingDisbursements</h3>
                <p class="text-muted mb-0">Ready for Disbursement</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="text-success mb-2">
                    <i class="bi bi-file-earmark-check" style="font-size: 2rem;"></i>
                </div>
                <h3 class="h4 mb-1">@Model.PendingAcquittals</h3>
                <p class="text-muted mb-0">Pending Acquittals</p>
            </div>
        </div>
    </div>
</div>

<!-- Financial Overview -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h5 class="card-title text-primary">
                    <i class="bi bi-graph-up me-2"></i>Total Requested
                </h5>
                <h2 class="text-primary mb-0">${@Model.TotalRequestedAmount:N2}</h2>
                <small class="text-muted">Across all applications</small>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <h5 class="card-title text-success">
                    <i class="bi bi-cash-stack me-2"></i>Total Disbursed
                </h5>
                <h2 class="text-success mb-0">${@Model.TotalDisbursedAmount:N2}</h2>
                <small class="text-muted">Successfully disbursed</small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Applications -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-file-earmark-text me-2"></i>Recent Applications
                    </h5>
                    <a asp-controller="AdminApplication" asp-action="Index" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if (Model.RecentApplications.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Company</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var app in Model.RecentApplications)
                                {
                                    <tr>
                                        <td><span class="badge bg-light text-dark">#@app.ApplicationID</span></td>
                                        <td>
                                            <div class="fw-medium">@app.CompanyName</div>
                                            <small class="text-muted">@app.Description</small>
                                        </td>
                                        <td class="fw-medium">${@app.RequestedCash:N2}</td>
                                        <td>
                                            @if (app.IsDisbursed)
                                            {
                                                <span class="badge bg-success">Disbursed</span>
                                            }
                                            else if (!app.Status)
                                            {
                                                <span class="badge bg-danger">Rejected</span>
                                            }
                                            else if (app.ApprovalLevel >= 4)
                                            {
                                                <span class="badge bg-info">Ready for Disbursement</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">Level @app.ApprovalLevel Approval</span>
                                            }
                                        </td>
                                        <td>
                                            <small>@app.DateRequested.ToString("MMM dd, yyyy")</small>
                                        </td>
                                        <td>
                                            <a asp-controller="AdminApplication" asp-action="Details" asp-route-id="@app.ApplicationID" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">No applications yet</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Recent Companies -->
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0 py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-building me-2"></i>Recent Companies
                    </h5>
                    <a asp-controller="AdminCompany" asp-action="Index" class="btn btn-outline-primary btn-sm">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if (Model.RecentCompanies.Any())
                {
                    @foreach (var company in Model.RecentCompanies)
                    {
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                            <div>
                                <div class="fw-medium">@company.Name</div>
                                <small class="text-muted">@company.Email</small>
                            </div>
                            <div>
                                @if (company.Status)
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Inactive</span>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="bi bi-building text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">No companies registered</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
