@model _CashDisbursement.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Login";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Zimdef Cash Disbursement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-floating > label {
            color: #6c757d;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-bank text-primary" style="font-size: 3rem;"></i>
                        <h2 class="mt-3 mb-1">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            @Html.ValidationSummary(false, "", new { @class = "mb-0" })
                        </div>
                    }

                    <form asp-action="Login" method="post">
                        <div class="form-floating mb-3">
                            <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                            <label asp-for="Email">Email address</label>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-floating mb-3">
                            <input asp-for="Password" class="form-control" placeholder="Password" />
                            <label asp-for="Password">Password</label>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Remember me
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-3 mb-3">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">Don't have an account?</p>
                        <a asp-action="Register" class="text-decoration-none">
                            <i class="bi bi-person-plus me-1"></i>Register your company
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
