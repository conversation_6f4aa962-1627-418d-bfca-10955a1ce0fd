@model _CashDisbursement.Models.ViewModels.CompanyRegistrationViewModel
@{
    ViewData["Title"] = "Register Company";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Register Company - Zimdef Cash Disbursement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .form-floating > label {
            color: #6c757d;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-card p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-building text-primary" style="font-size: 3rem;"></i>
                        <h2 class="mt-3 mb-1">Register Your Company</h2>
                        <p class="text-muted">Create an account to access the cash disbursement system</p>
                    </div>

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Please correct the errors below.
                        </div>
                    }

                    <form asp-action="Register" method="post">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-building me-2"></i>Company Information
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="CompanyName" class="form-control" placeholder="Company Name" />
                                    <label asp-for="CompanyName">Company Name</label>
                                    <span asp-validation-for="CompanyName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="CompanyEmail" class="form-control" placeholder="<EMAIL>" />
                                    <label asp-for="CompanyEmail">Company Email</label>
                                    <span asp-validation-for="CompanyEmail" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="Contact" class="form-control" placeholder="Phone Number" />
                                    <label asp-for="Contact">Contact Number</label>
                                    <span asp-validation-for="Contact" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <textarea asp-for="Address" class="form-control" placeholder="Company Address" style="height: 58px;"></textarea>
                                    <label asp-for="Address">Address</label>
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-person-badge me-2"></i>Administrator Account
                                </h5>
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input asp-for="AdminEmail" class="form-control" placeholder="<EMAIL>" />
                            <label asp-for="AdminEmail">Administrator Email</label>
                            <span asp-validation-for="AdminEmail" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="Password" class="form-control" placeholder="Password" />
                                    <label asp-for="Password">Password</label>
                                    <span asp-validation-for="Password" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm Password" />
                                    <label asp-for="ConfirmPassword">Confirm Password</label>
                                    <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-3 mb-3">
                            <i class="bi bi-person-plus me-2"></i>Register Company
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-0">Already have an account?</p>
                        <a asp-action="Login" class="text-decoration-none">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Sign in here
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
