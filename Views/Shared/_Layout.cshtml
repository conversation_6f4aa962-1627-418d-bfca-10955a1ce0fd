﻿@using _CashDisbursement.Utilities
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Zimdef Cash Disbursement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="container-fluid px-4">
                <a class="navbar-brand fw-bold d-flex align-items-center" href="/" style="font-size: 1.4rem;">
                    <div class="bg-white rounded-circle p-2 me-3 shadow-sm">
                        <i class="bi bi-bank text-primary" style="font-size: 1.2rem;"></i>
                    </div>
                    <div>
                        <div class="fw-bold">Zimdef</div>
                        <small style="font-size: 0.75rem; opacity: 0.9;">Cash Disbursement</small>
                    </div>
                </a>
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (SessionHelper.IsAuthenticated(Context.Session))
                    {
                        var user = SessionHelper.GetUser(Context.Session);
                        var company = SessionHelper.GetCompany(Context.Session);

                        @if (SessionHelper.IsCompanyUser(Context.Session))
                        {
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <a class="nav-link px-3 py-2 rounded-pill mx-1 nav-link-modern" asp-controller="CompanyDashboard" asp-action="Index">
                                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link px-3 py-2 rounded-pill mx-1 nav-link-modern" asp-controller="Application" asp-action="Index">
                                        <i class="bi bi-file-earmark-text me-2"></i>My Applications
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link px-3 py-2 rounded-pill mx-1 nav-link-modern bg-white text-primary fw-medium" asp-controller="Application" asp-action="Create">
                                        <i class="bi bi-plus-circle me-2"></i>New Application
                                    </a>
                                </li>
                            </ul>
                        }
                        else if (SessionHelper.IsAdminUser(Context.Session))
                        {
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <a class="nav-link px-3 py-2 rounded-pill mx-1 nav-link-modern" asp-controller="AdminDashboard" asp-action="Index">
                                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle px-3 py-2 rounded-pill mx-1 nav-link-modern" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="bi bi-file-earmark-check me-2"></i>Applications
                                    </a>
                                    <ul class="dropdown-menu shadow border-0">
                                        <li><a class="dropdown-item" asp-controller="AdminApplication" asp-action="Index">
                                            <i class="bi bi-list me-2"></i>All Applications</a></li>
                                        <li><a class="dropdown-item" asp-controller="AdminApplication" asp-action="PendingApprovals">
                                            <i class="bi bi-clock me-2"></i>Pending Approvals</a></li>
                                        <li><a class="dropdown-item" asp-controller="AdminApplication" asp-action="PendingDisbursements">
                                            <i class="bi bi-cash-coin me-2"></i>Ready for Disbursement</a></li>
                                    </ul>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link px-3 py-2 rounded-pill mx-1 nav-link-modern" asp-controller="AdminCompany" asp-action="Index">
                                        <i class="bi bi-building me-2"></i>Companies
                                    </a>
                                </li>
                            </ul>
                        }

                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle px-3 py-2 rounded-pill bg-white bg-opacity-10 text-white" href="#" role="button" data-bs-toggle="dropdown">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-white rounded-circle p-1 me-2">
                                            <i class="bi bi-person-circle text-primary"></i>
                                        </div>
                                        <div class="d-none d-md-block">
                                            @if (company != null)
                                            {
                                                <div class="fw-medium" style="font-size: 0.9rem;">@company.Name</div>
                                                <small style="font-size: 0.75rem; opacity: 0.8;">@user?.Email</small>
                                            }
                                            else
                                            {
                                                <div class="fw-medium" style="font-size: 0.9rem;">@user?.Email</div>
                                                <small style="font-size: 0.75rem; opacity: 0.8;">@user?.Role</small>
                                            }
                                        </div>
                                    </div>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end shadow border-0" style="min-width: 250px;">
                                    <li class="px-3 py-2 bg-light">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary rounded-circle p-2 me-3">
                                                <i class="bi bi-person text-white"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">@user?.Email</div>
                                                <small class="text-muted">@user?.Role</small>
                                                @if (company != null)
                                                {
                                                    <br><small class="text-primary">@company.Name</small>
                                                }
                                            </div>
                                        </div>
                                    </li>
                                    <li><hr class="dropdown-divider my-2"></li>
                                    <li>
                                        <form asp-controller="Auth" asp-action="Logout" method="post" class="d-inline w-100">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    }
                    else
                    {
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Login">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Register">
                                    <i class="bi bi-person-plus me-1"></i>Register
                                </a>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <main class="flex-grow-1" style="background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%); min-height: calc(100vh - 140px);">
        <div class="container-fluid py-4" style="max-width: 1400px;">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show shadow-sm border-0 mb-4" role="alert" style="border-left: 4px solid #28a745 !important;">
                    <div class="d-flex align-items-center">
                        <div class="bg-success rounded-circle p-2 me-3">
                            <i class="bi bi-check-circle text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <strong>Success!</strong> @TempData["SuccessMessage"]
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show shadow-sm border-0 mb-4" role="alert" style="border-left: 4px solid #dc3545 !important;">
                    <div class="d-flex align-items-center">
                        <div class="bg-danger rounded-circle p-2 me-3">
                            <i class="bi bi-exclamation-triangle text-white"></i>
                        </div>
                        <div class="flex-grow-1">
                            <strong>Error!</strong> @TempData["ErrorMessage"]
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            }
            <div class="fade-in">
                @RenderBody()
            </div>
        </div>
    </main>

    <footer class="bg-light border-top mt-auto py-3">
        <div class="container text-center text-muted">
            <small>&copy; 2025 Zimdef Cash Disbursement System. All rights reserved.</small>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
