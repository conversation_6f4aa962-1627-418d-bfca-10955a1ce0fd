﻿@using _CashDisbursement.Utilities
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Zimdef Cash Disbursement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/">
                    <i class="bi bi-bank me-2"></i>Zimdef Cash Disbursement
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (SessionHelper.IsAuthenticated(Context.Session))
                    {
                        var user = SessionHelper.GetUser(Context.Session);
                        var company = SessionHelper.GetCompany(Context.Session);

                        @if (SessionHelper.IsCompanyUser(Context.Session))
                        {
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="CompanyDashboard" asp-action="Index">
                                        <i class="bi bi-speedometer2 me-1"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Application" asp-action="Index">
                                        <i class="bi bi-file-earmark-text me-1"></i>Applications
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Application" asp-action="Create">
                                        <i class="bi bi-plus-circle me-1"></i>New Application
                                    </a>
                                </li>
                            </ul>
                        }
                        else if (SessionHelper.IsAdminUser(Context.Session))
                        {
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="AdminDashboard" asp-action="Index">
                                        <i class="bi bi-speedometer2 me-1"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="AdminApplication" asp-action="Index">
                                        <i class="bi bi-file-earmark-check me-1"></i>Applications
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="AdminCompany" asp-action="Index">
                                        <i class="bi bi-building me-1"></i>Companies
                                    </a>
                                </li>
                            </ul>
                        }

                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-1"></i>
                                    @if (company != null)
                                    {
                                        @company.Name
                                    }
                                    else
                                    {
                                        @user?.Email
                                    }
                                </a>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">@user?.Email</h6></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Auth" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right me-1"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    }
                    else
                    {
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Login">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Register">
                                    <i class="bi bi-person-plus me-1"></i>Register
                                </a>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <main class="flex-grow-1">
        <div class="container-fluid py-4">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @RenderBody()
        </div>
    </main>

    <footer class="bg-light border-top mt-auto py-3">
        <div class="container text-center text-muted">
            <small>&copy; 2025 Zimdef Cash Disbursement System. All rights reserved.</small>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
