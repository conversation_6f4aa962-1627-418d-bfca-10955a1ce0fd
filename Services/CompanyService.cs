using _CashDisbursement.Data.Repositories;
using _CashDisbursement.Models.Entities;
using _CashDisbursement.Models.ViewModels;

namespace _CashDisbursement.Services
{
    public interface ICompanyService
    {
        Task<Company?> GetByIdAsync(int id);
        Task<Company?> GetByEmailAsync(string email);
        Task<IEnumerable<Company>> GetAllCompaniesAsync();
        Task<IEnumerable<Company>> GetActiveCompaniesAsync();
        Task<Company> CreateCompanyAsync(Company company);
        Task<Company> UpdateCompanyAsync(Company company);
        Task<bool> DeleteCompanyAsync(int id);
        Task<bool> EmailExistsAsync(string email);
        Task<(bool Success, Company? Company, User? AdminUser)> RegisterCompanyWithAdminAsync(CompanyRegistrationViewModel model);
    }
    
    public class CompanyService : ICompanyService
    {
        private readonly ICompanyRepository _companyRepository;
        private readonly IUserRepository _userRepository;
        private readonly ILogService _logService;
        
        public CompanyService(
            ICompanyRepository companyRepository, 
            IUserRepository userRepository,
            ILogService logService)
        {
            _companyRepository = companyRepository;
            _userRepository = userRepository;
            _logService = logService;
        }
        
        public async Task<Company?> GetByIdAsync(int id)
        {
            return await _companyRepository.GetByIdAsync(id);
        }
        
        public async Task<Company?> GetByEmailAsync(string email)
        {
            return await _companyRepository.GetByEmailAsync(email);
        }
        
        public async Task<IEnumerable<Company>> GetAllCompaniesAsync()
        {
            return await _companyRepository.GetAllAsync();
        }
        
        public async Task<IEnumerable<Company>> GetActiveCompaniesAsync()
        {
            return await _companyRepository.GetActiveCompaniesAsync();
        }
        
        public async Task<Company> CreateCompanyAsync(Company company)
        {
            return await _companyRepository.AddAsync(company);
        }
        
        public async Task<Company> UpdateCompanyAsync(Company company)
        {
            return await _companyRepository.UpdateAsync(company);
        }
        
        public async Task<bool> DeleteCompanyAsync(int id)
        {
            return await _companyRepository.DeleteAsync(id);
        }
        
        public async Task<bool> EmailExistsAsync(string email)
        {
            return await _companyRepository.EmailExistsAsync(email);
        }
        
        public async Task<(bool Success, Company? Company, User? AdminUser)> RegisterCompanyWithAdminAsync(CompanyRegistrationViewModel model)
        {
            // Check if company email already exists
            if (await _companyRepository.EmailExistsAsync(model.CompanyEmail))
            {
                return (false, null, null);
            }
            
            // Check if admin email already exists
            if (await _userRepository.EmailExistsAsync(model.AdminEmail))
            {
                return (false, null, null);
            }
            
            try
            {
                // Create company first
                var company = new Company
                {
                    Name = model.CompanyName,
                    Email = model.CompanyEmail,
                    Contact = model.Contact,
                    Address = model.Address,
                    Status = true
                };
                
                var createdCompany = await _companyRepository.AddAsync(company);
                
                // Create admin user
                var adminUser = new User
                {
                    Email = model.AdminEmail,
                    Password = model.Password, // In production, hash this
                    Status = true,
                    CompanyID = createdCompany.CompanyID,
                    Role = UserRoles.CompanyAdmin
                };
                
                var createdUser = await _userRepository.AddAsync(adminUser);
                
                // Log the registration
                await _logService.LogAsync(createdUser.UserID, $"Company '{company.Name}' registered with admin user '{adminUser.Email}'");
                
                return (true, createdCompany, createdUser);
            }
            catch (Exception)
            {
                // If user creation fails, we should ideally rollback the company creation
                // For now, just return failure
                return (false, null, null);
            }
        }
    }
}
