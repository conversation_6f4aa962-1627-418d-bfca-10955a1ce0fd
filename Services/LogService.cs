using System.Data;
using System.Data.SqlClient;
using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Services
{
    public interface ILogService
    {
        Task LogAsync(int userId, string description);
        Task<IEnumerable<Log>> GetLogsByUserIdAsync(int userId);
        Task<IEnumerable<Log>> GetRecentLogsAsync(int count = 50);
    }
    
    public class LogService : ILogService
    {
        private readonly IDatabaseConnection _dbConnection;
        
        public LogService(IDatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }
        
        public async Task LogAsync(int userId, string description)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                INSERT INTO Logs (Description, Date, UserID) 
                VALUES (@Description, @Date, @UserID)", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Description", description);
            command.Parameters.AddWithValue("@Date", DateTime.Now);
            command.Parameters.AddWithValue("@UserID", userId);
            
            await command.ExecuteNonQueryAsync();
        }
        
        public async Task<IEnumerable<Log>> GetLogsByUserIdAsync(int userId)
        {
            var logs = new List<Log>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT l.*, u.Email as UserEmail 
                FROM Logs l 
                INNER JOIN Users u ON l.UserID = u.UserID 
                WHERE l.UserID = @UserID 
                ORDER BY l.Date DESC", (SqlConnection)connection);
            command.Parameters.AddWithValue("@UserID", userId);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                logs.Add(MapToLog(reader));
            }
            return logs;
        }
        
        public async Task<IEnumerable<Log>> GetRecentLogsAsync(int count = 50)
        {
            var logs = new List<Log>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT TOP (@Count) l.*, u.Email as UserEmail 
                FROM Logs l 
                INNER JOIN Users u ON l.UserID = u.UserID 
                ORDER BY l.Date DESC", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Count", count);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                logs.Add(MapToLog(reader));
            }
            return logs;
        }
        
        private static Log MapToLog(SqlDataReader reader)
        {
            var log = new Log
            {
                LogID = reader.GetInt32("LogID"),
                Description = reader.GetString("Description"),
                Date = reader.GetDateTime("Date"),
                UserID = reader.GetInt32("UserID")
            };
            
            // Set user if available
            if (!reader.IsDBNull("UserEmail"))
            {
                log.User = new User
                {
                    UserID = log.UserID,
                    Email = reader.GetString("UserEmail")
                };
            }
            
            return log;
        }
    }
}
