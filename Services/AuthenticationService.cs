using _CashDisbursement.Data.Repositories;
using _CashDisbursement.Models.Entities;
using System.Security.Cryptography;
using System.Text;

namespace _CashDisbursement.Services
{
    public interface IAuthenticationService
    {
        Task<User?> AuthenticateAsync(string email, string password);
        Task<bool> IsInRoleAsync(User user, string role);
        Task<bool> IsCompanyUserAsync(User user);
        Task<bool> IsAdminUserAsync(User user);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hashedPassword);
    }
    
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUserRepository _userRepository;
        
        public AuthenticationService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }
        
        public async Task<User?> AuthenticateAsync(string email, string password)
        {
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
                return null;
            
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null || !user.Status)
                return null;
            
            // For now, using simple password comparison
            // In production, use proper password hashing
            if (user.Password == password)
                return user;
            
            return null;
        }
        
        public async Task<bool> IsInRoleAsync(User user, string role)
        {
            return await Task.FromResult(user.Role.Equals(role, StringComparison.OrdinalIgnoreCase));
        }
        
        public async Task<bool> IsCompanyUserAsync(User user)
        {
            return await Task.FromResult(
                user.Role == UserRoles.CompanyAdmin || 
                user.Role == UserRoles.CompanyUser
            );
        }
        
        public async Task<bool> IsAdminUserAsync(User user)
        {
            return await Task.FromResult(
                user.Role == UserRoles.SystemAdmin || 
                user.Role == UserRoles.Approver || 
                user.Role == UserRoles.Disbursement
            );
        }
        
        public string HashPassword(string password)
        {
            // Simple implementation - in production use BCrypt or similar
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
        
        public bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput == hashedPassword;
        }
    }
}
