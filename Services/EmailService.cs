using MailKit.Net.Smtp;
using MimeKit;
using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Services
{
    public interface IEmailService
    {
        Task SendApplicationSubmittedNotificationAsync(Application application);
        Task SendApplicationApprovedNotificationAsync(Application application, int approvalLevel);
        Task SendApplicationRejectedNotificationAsync(Application application, string reason);
        Task SendApplicationDisbursedNotificationAsync(Application application);
        Task SendAcquittalRequestNotificationAsync(Application application);
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
    }
    
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        
        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }
        
        public async Task SendApplicationSubmittedNotificationAsync(Application application)
        {
            var subject = $"New Fund Application Submitted - {application.Company?.Name}";
            var body = $@"
                <h3>New Fund Application Submitted</h3>
                <p><strong>Company:</strong> {application.Company?.Name}</p>
                <p><strong>Application ID:</strong> {application.ApplicationID}</p>
                <p><strong>Description:</strong> {application.Description}</p>
                <p><strong>Purpose:</strong> {application.Purpose}</p>
                <p><strong>Requested Amount:</strong> ${application.RequestedCash:N2}</p>
                <p><strong>Date Requested:</strong> {application.DateRequested:yyyy-MM-dd HH:mm}</p>
                <p>Please review and approve this application in the admin portal.</p>
            ";
            
            // Send to all approvers - you might want to get this from ApprovalList table
            await SendEmailAsync("<EMAIL>", subject, body);
        }
        
        public async Task SendApplicationApprovedNotificationAsync(Application application, int approvalLevel)
        {
            var subject = $"Fund Application Approved - Level {approvalLevel}";
            var body = $@"
                <h3>Fund Application Approved</h3>
                <p><strong>Company:</strong> {application.Company?.Name}</p>
                <p><strong>Application ID:</strong> {application.ApplicationID}</p>
                <p><strong>Approval Level:</strong> {approvalLevel}</p>
                <p><strong>Requested Amount:</strong> ${application.RequestedCash:N2}</p>
                <p><strong>Comments:</strong> {application.ApprovalComment}</p>
                <p>Your application has been approved and is progressing through the approval workflow.</p>
            ";
            
            await SendEmailAsync(application.Company?.Email ?? "", subject, body);
        }
        
        public async Task SendApplicationRejectedNotificationAsync(Application application, string reason)
        {
            var subject = $"Fund Application Rejected - {application.Company?.Name}";
            var body = $@"
                <h3>Fund Application Rejected</h3>
                <p><strong>Company:</strong> {application.Company?.Name}</p>
                <p><strong>Application ID:</strong> {application.ApplicationID}</p>
                <p><strong>Requested Amount:</strong> ${application.RequestedCash:N2}</p>
                <p><strong>Reason for Rejection:</strong> {reason}</p>
                <p>Unfortunately, your fund application has been rejected. Please contact us for more information.</p>
            ";
            
            await SendEmailAsync(application.Company?.Email ?? "", subject, body);
        }
        
        public async Task SendApplicationDisbursedNotificationAsync(Application application)
        {
            var subject = $"Funds Disbursed - {application.Company?.Name}";
            var body = $@"
                <h3>Funds Successfully Disbursed</h3>
                <p><strong>Company:</strong> {application.Company?.Name}</p>
                <p><strong>Application ID:</strong> {application.ApplicationID}</p>
                <p><strong>Disbursed Amount:</strong> ${application.RequestedCash:N2}</p>
                <p><strong>Disbursed By:</strong> {application.DisbursedBy}</p>
                <p><strong>Date Disbursed:</strong> {application.DateDisbursed:yyyy-MM-dd HH:mm}</p>
                <p>The funds have been successfully disbursed. Please submit your acquittal documentation when ready.</p>
            ";
            
            await SendEmailAsync(application.Company?.Email ?? "", subject, body);
        }
        
        public async Task SendAcquittalRequestNotificationAsync(Application application)
        {
            var subject = $"Acquittal Required - {application.Company?.Name}";
            var body = $@"
                <h3>Acquittal Documentation Required</h3>
                <p><strong>Company:</strong> {application.Company?.Name}</p>
                <p><strong>Application ID:</strong> {application.ApplicationID}</p>
                <p><strong>Disbursed Amount:</strong> ${application.RequestedCash:N2}</p>
                <p><strong>Date Disbursed:</strong> {application.DateDisbursed:yyyy-MM-dd HH:mm}</p>
                <p>Please submit your acquittal documentation for the disbursed funds through the company portal.</p>
            ";
            
            await SendEmailAsync(application.Company?.Email ?? "", subject, body);
        }
        
        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            try
            {
                var emailSettings = _configuration.GetSection("EmailSettings");
                var smtpServer = emailSettings["SmtpServer"];
                var smtpPort = int.Parse(emailSettings["SmtpPort"] ?? "587");
                var senderEmail = emailSettings["SenderEmail"];
                var senderName = emailSettings["SenderName"];
                var username = emailSettings["Username"];
                var password = emailSettings["Password"];
                
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(senderName, senderEmail));
                message.To.Add(new MailboxAddress("", to));
                message.Subject = subject;
                
                var bodyBuilder = new BodyBuilder();
                if (isHtml)
                {
                    bodyBuilder.HtmlBody = body;
                }
                else
                {
                    bodyBuilder.TextBody = body;
                }
                message.Body = bodyBuilder.ToMessageBody();
                
                using var client = new SmtpClient();
                await client.ConnectAsync(smtpServer, smtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(username, password);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);
                
                _logger.LogInformation($"Email sent successfully to {to}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email to {to}");
                // Don't throw - email failures shouldn't break the application flow
            }
        }
    }
}
