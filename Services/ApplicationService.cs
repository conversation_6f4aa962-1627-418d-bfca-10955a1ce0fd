using _CashDisbursement.Data.Repositories;
using _CashDisbursement.Models.Entities;
using _CashDisbursement.Models.ViewModels;

namespace _CashDisbursement.Services
{
    public interface IApplicationService
    {
        Task<Application?> GetByIdAsync(int id);
        Task<IEnumerable<Application>> GetByCompanyIdAsync(int companyId);
        Task<IEnumerable<Application>> GetPendingApplicationsAsync();
        Task<IEnumerable<Application>> GetApplicationsByApprovalLevelAsync(int approvalLevel);
        Task<Application> CreateApplicationAsync(ApplicationViewModel model, int companyId);
        Task<bool> ApproveApplicationAsync(int applicationId, int newApprovalLevel, string comment, int approvedBy);
        Task<bool> RejectApplicationAsync(int applicationId, string comment, int rejectedBy);
        Task<bool> DisburseApplicationAsync(int applicationId, string disbursedBy);
        Task<ApplicationViewModel> MapToViewModelAsync(Application application);
        Task<string> SaveSupportingDocumentAsync(IFormFile file, int applicationId);
    }
    
    public class ApplicationService : IApplicationService
    {
        private readonly IApplicationRepository _applicationRepository;
        private readonly ICompanyRepository _companyRepository;
        private readonly ILogService _logService;
        private readonly IEmailService _emailService;
        private readonly IWebHostEnvironment _environment;
        
        public ApplicationService(
            IApplicationRepository applicationRepository,
            ICompanyRepository companyRepository,
            ILogService logService,
            IEmailService emailService,
            IWebHostEnvironment environment)
        {
            _applicationRepository = applicationRepository;
            _companyRepository = companyRepository;
            _logService = logService;
            _emailService = emailService;
            _environment = environment;
        }
        
        public async Task<Application?> GetByIdAsync(int id)
        {
            return await _applicationRepository.GetByIdAsync(id);
        }
        
        public async Task<IEnumerable<Application>> GetByCompanyIdAsync(int companyId)
        {
            return await _applicationRepository.GetByCompanyIdAsync(companyId);
        }
        
        public async Task<IEnumerable<Application>> GetPendingApplicationsAsync()
        {
            return await _applicationRepository.GetPendingApplicationsAsync();
        }
        
        public async Task<IEnumerable<Application>> GetApplicationsByApprovalLevelAsync(int approvalLevel)
        {
            return await _applicationRepository.GetApplicationsByApprovalLevelAsync(approvalLevel);
        }
        
        public async Task<Application> CreateApplicationAsync(ApplicationViewModel model, int companyId)
        {
            var application = new Application
            {
                CompanyID = companyId,
                Description = model.Description,
                Purpose = model.Purpose,
                RequestedCash = model.RequestedCash,
                DateRequested = DateTime.Now,
                Status = true,
                ApprovalLevel = 0,
                IsDisbursed = false
            };
            
            // Save supporting document if provided
            if (model.SupportingDocument != null)
            {
                var filePath = await SaveSupportingDocumentAsync(model.SupportingDocument, 0); // Will update with actual ID after creation
                application.PDFLocation = filePath;
            }
            
            var createdApplication = await _applicationRepository.AddAsync(application);
            
            // Update file path with actual application ID if document was uploaded
            if (!string.IsNullOrEmpty(application.PDFLocation))
            {
                var newFilePath = application.PDFLocation.Replace("_0_", $"_{createdApplication.ApplicationID}_");
                var oldFullPath = Path.Combine(_environment.WebRootPath, "uploads", Path.GetFileName(application.PDFLocation));
                var newFullPath = Path.Combine(_environment.WebRootPath, "uploads", Path.GetFileName(newFilePath));
                
                if (File.Exists(oldFullPath))
                {
                    File.Move(oldFullPath, newFullPath);
                    createdApplication.PDFLocation = newFilePath;
                    await _applicationRepository.UpdateAsync(createdApplication);
                }
            }
            
            // Send notification email to approvers
            await _emailService.SendApplicationSubmittedNotificationAsync(createdApplication);
            
            return createdApplication;
        }
        
        public async Task<bool> ApproveApplicationAsync(int applicationId, int newApprovalLevel, string comment, int approvedBy)
        {
            var application = await _applicationRepository.GetByIdAsync(applicationId);
            if (application == null) return false;
            
            var success = await _applicationRepository.UpdateApprovalLevelAsync(applicationId, newApprovalLevel, comment);
            
            if (success)
            {
                await _logService.LogAsync(approvedBy, $"Application {applicationId} approved to level {newApprovalLevel}. Comment: {comment}");
                
                // Send notification email
                var updatedApplication = await _applicationRepository.GetByIdAsync(applicationId);
                if (updatedApplication != null)
                {
                    await _emailService.SendApplicationApprovedNotificationAsync(updatedApplication, newApprovalLevel);
                }
            }
            
            return success;
        }
        
        public async Task<bool> RejectApplicationAsync(int applicationId, string comment, int rejectedBy)
        {
            var application = await _applicationRepository.GetByIdAsync(applicationId);
            if (application == null) return false;
            
            application.Status = false;
            application.ApprovalComment = comment;
            
            var success = await _applicationRepository.UpdateAsync(application);
            
            if (success != null)
            {
                await _logService.LogAsync(rejectedBy, $"Application {applicationId} rejected. Comment: {comment}");
                await _emailService.SendApplicationRejectedNotificationAsync(application, comment);
            }
            
            return success != null;
        }
        
        public async Task<bool> DisburseApplicationAsync(int applicationId, string disbursedBy)
        {
            var success = await _applicationRepository.MarkAsDisbursedAsync(applicationId, disbursedBy);
            
            if (success)
            {
                var application = await _applicationRepository.GetByIdAsync(applicationId);
                if (application != null)
                {
                    await _emailService.SendApplicationDisbursedNotificationAsync(application);
                }
            }
            
            return success;
        }
        
        public async Task<ApplicationViewModel> MapToViewModelAsync(Application application)
        {
            return new ApplicationViewModel
            {
                ApplicationID = application.ApplicationID,
                Description = application.Description,
                Purpose = application.Purpose,
                RequestedCash = application.RequestedCash,
                CompanyName = application.Company?.Name,
                DateRequested = application.DateRequested,
                DateDisbursed = application.DateDisbursed,
                IsDisbursed = application.IsDisbursed,
                DisbursedBy = application.DisbursedBy,
                ApprovalLevel = application.ApprovalLevel,
                ApprovalComment = application.ApprovalComment,
                PDFLocation = application.PDFLocation,
                Status = application.Status
            };
        }
        
        public async Task<string> SaveSupportingDocumentAsync(IFormFile file, int applicationId)
        {
            if (file == null || file.Length == 0)
                return string.Empty;
            
            var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads");
            if (!Directory.Exists(uploadsFolder))
                Directory.CreateDirectory(uploadsFolder);
            
            var fileName = $"app_{applicationId}_{DateTime.Now:yyyyMMddHHmmss}_{file.FileName}";
            var filePath = Path.Combine(uploadsFolder, fileName);
            
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(fileStream);
            }
            
            return $"uploads/{fileName}";
        }
    }
}
