using System.Data;
using Microsoft.Data.SqlClient;

namespace _CashDisbursement.Data
{
    public interface IDatabaseConnection
    {
        IDbConnection CreateConnection();
        Task<IDbConnection> CreateConnectionAsync();
    }
    
    public class DatabaseConnection : IDatabaseConnection
    {
        private readonly string _connectionString;
        
        public DatabaseConnection(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration), "Connection string not found");
        }
        
        public IDbConnection CreateConnection()
        {
            return new SqlConnection(_connectionString);
        }
        
        public async Task<IDbConnection> CreateConnectionAsync()
        {
            var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            return connection;
        }
    }
}
