using System.Data;
using Microsoft.Data.SqlClient;
using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Data.Repositories
{
    public interface IUserRepository : IRepository<User>
    {
        Task<User?> GetByEmailAsync(string email);
        Task<User?> AuthenticateAsync(string email, string password);
        Task<IEnumerable<User>> GetByCompanyIdAsync(int companyId);
        Task<IEnumerable<User>> GetByRoleAsync(string role);
        Task<bool> EmailExistsAsync(string email);
        Task<bool> ChangePasswordAsync(int userId, string newPassword);
    }
    
    public class UserRepository : IUserRepository
    {
        private readonly IDatabaseConnection _dbConnection;
        
        public UserRepository(IDatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }
        
        public async Task<User?> GetByIdAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                WHERE u.UserID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToUser(reader);
            }
            return null;
        }
        
        public async Task<User?> GetByEmailAsync(string email)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                WHERE u.Email = @Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Email", email);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToUser(reader);
            }
            return null;
        }
        
        public async Task<User?> AuthenticateAsync(string email, string password)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                WHERE u.Email = @Email AND u.Password = @Password AND u.Status = 1", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Email", email);
            command.Parameters.AddWithValue("@Password", password); // Note: In production, use proper password hashing
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToUser(reader);
            }
            return null;
        }
        
        public async Task<IEnumerable<User>> GetAllAsync()
        {
            var users = new List<User>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                ORDER BY u.Email", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                users.Add(MapToUser(reader));
            }
            return users;
        }
        
        public async Task<IEnumerable<User>> GetByCompanyIdAsync(int companyId)
        {
            var users = new List<User>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                WHERE u.CompanyID = @CompanyId 
                ORDER BY u.Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@CompanyId", companyId);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                users.Add(MapToUser(reader));
            }
            return users;
        }
        
        public async Task<IEnumerable<User>> GetByRoleAsync(string role)
        {
            var users = new List<User>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT u.*, c.Name as CompanyName 
                FROM Users u 
                LEFT JOIN Company c ON u.CompanyID = c.CompanyID 
                WHERE u.Role = @Role AND u.Status = 1
                ORDER BY u.Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Role", role);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                users.Add(MapToUser(reader));
            }
            return users;
        }
        
        public async Task<User> AddAsync(User entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                INSERT INTO Users (Email, Password, Status, CompanyID, Role) 
                OUTPUT INSERTED.UserID
                VALUES (@Email, @Password, @Status, @CompanyID, @Role)", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Email", entity.Email);
            command.Parameters.AddWithValue("@Password", entity.Password);
            command.Parameters.AddWithValue("@Status", entity.Status);
            command.Parameters.AddWithValue("@CompanyID", entity.CompanyID.HasValue ? entity.CompanyID.Value : (object)DBNull.Value);
            command.Parameters.AddWithValue("@Role", entity.Role);
            
            var id = await command.ExecuteScalarAsync();
            entity.UserID = Convert.ToInt32(id);
            return entity;
        }
        
        public async Task<User> UpdateAsync(User entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                UPDATE Users 
                SET Email = @Email, Password = @Password, Status = @Status, CompanyID = @CompanyID, Role = @Role
                WHERE UserID = @Id", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Id", entity.UserID);
            command.Parameters.AddWithValue("@Email", entity.Email);
            command.Parameters.AddWithValue("@Password", entity.Password);
            command.Parameters.AddWithValue("@Status", entity.Status);
            command.Parameters.AddWithValue("@CompanyID", entity.CompanyID.HasValue ? entity.CompanyID.Value : (object)DBNull.Value);
            command.Parameters.AddWithValue("@Role", entity.Role);
            
            await command.ExecuteNonQueryAsync();
            return entity;
        }
        
        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("DELETE FROM Users WHERE UserID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        public async Task<bool> ExistsAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT COUNT(1) FROM Users WHERE UserID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var count = await command.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        
        public async Task<bool> EmailExistsAsync(string email)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT COUNT(1) FROM Users WHERE Email = @Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Email", email);
            
            var count = await command.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        
        public async Task<bool> ChangePasswordAsync(int userId, string newPassword)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("UPDATE Users SET Password = @Password WHERE UserID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", userId);
            command.Parameters.AddWithValue("@Password", newPassword);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        private static User MapToUser(SqlDataReader reader)
        {
            var user = new User
            {
                UserID = reader.GetInt32("UserID"),
                Email = reader.GetString("Email"),
                Password = reader.GetString("Password"),
                Status = reader.GetBoolean("Status"),
                CompanyID = reader.IsDBNull("CompanyID") ? null : reader.GetInt32("CompanyID"),
                Role = reader.GetString("Role")
            };
            
            // Set company if available
            if (!reader.IsDBNull("CompanyName"))
            {
                user.Company = new Company
                {
                    CompanyID = user.CompanyID ?? 0,
                    Name = reader.GetString("CompanyName")
                };
            }
            
            return user;
        }
    }
}
