using System.Data;
using System.Data.SqlClient;
using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Data.Repositories
{
    public interface IApplicationRepository : IRepository<Application>
    {
        Task<IEnumerable<Application>> GetByCompanyIdAsync(int companyId);
        Task<IEnumerable<Application>> GetPendingApplicationsAsync();
        Task<IEnumerable<Application>> GetApplicationsByApprovalLevelAsync(int approvalLevel);
        Task<IEnumerable<Application>> GetDisbursedApplicationsAsync();
        Task<bool> UpdateApprovalLevelAsync(int applicationId, int approvalLevel, string? comment);
        Task<bool> MarkAsDisbursedAsync(int applicationId, string disbursedBy);
    }
    
    public class ApplicationRepository : IApplicationRepository
    {
        private readonly IDatabaseConnection _dbConnection;
        
        public ApplicationRepository(IDatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }
        
        public async Task<Application?> GetByIdAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                WHERE a.ApplicationID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToApplication(reader);
            }
            return null;
        }
        
        public async Task<IEnumerable<Application>> GetAllAsync()
        {
            var applications = new List<Application>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                ORDER BY a.DateRequested DESC", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                applications.Add(MapToApplication(reader));
            }
            return applications;
        }
        
        public async Task<IEnumerable<Application>> GetByCompanyIdAsync(int companyId)
        {
            var applications = new List<Application>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                WHERE a.CompanyID = @CompanyId 
                ORDER BY a.DateRequested DESC", (SqlConnection)connection);
            command.Parameters.AddWithValue("@CompanyId", companyId);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                applications.Add(MapToApplication(reader));
            }
            return applications;
        }
        
        public async Task<IEnumerable<Application>> GetPendingApplicationsAsync()
        {
            var applications = new List<Application>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                WHERE a.Status = 1 AND a.IsDisbursed = 0 
                ORDER BY a.DateRequested ASC", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                applications.Add(MapToApplication(reader));
            }
            return applications;
        }
        
        public async Task<IEnumerable<Application>> GetApplicationsByApprovalLevelAsync(int approvalLevel)
        {
            var applications = new List<Application>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                WHERE a.ApprovalLevel = @ApprovalLevel AND a.Status = 1 AND a.IsDisbursed = 0 
                ORDER BY a.DateRequested ASC", (SqlConnection)connection);
            command.Parameters.AddWithValue("@ApprovalLevel", approvalLevel);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                applications.Add(MapToApplication(reader));
            }
            return applications;
        }
        
        public async Task<IEnumerable<Application>> GetDisbursedApplicationsAsync()
        {
            var applications = new List<Application>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                SELECT a.*, c.Name as CompanyName 
                FROM Applications a 
                INNER JOIN Company c ON a.CompanyID = c.CompanyID 
                WHERE a.IsDisbursed = 1 
                ORDER BY a.DateDisbursed DESC", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                applications.Add(MapToApplication(reader));
            }
            return applications;
        }
        
        public async Task<Application> AddAsync(Application entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                INSERT INTO Applications (CompanyID, Description, Purpose, RequestedCash, IsDisbursed, DateRequested, Status, ApprovalLevel, PDFLocation) 
                OUTPUT INSERTED.ApplicationID
                VALUES (@CompanyID, @Description, @Purpose, @RequestedCash, @IsDisbursed, @DateRequested, @Status, @ApprovalLevel, @PDFLocation)", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@CompanyID", entity.CompanyID);
            command.Parameters.AddWithValue("@Description", entity.Description);
            command.Parameters.AddWithValue("@Purpose", entity.Purpose);
            command.Parameters.AddWithValue("@RequestedCash", entity.RequestedCash);
            command.Parameters.AddWithValue("@IsDisbursed", entity.IsDisbursed);
            command.Parameters.AddWithValue("@DateRequested", entity.DateRequested);
            command.Parameters.AddWithValue("@Status", entity.Status);
            command.Parameters.AddWithValue("@ApprovalLevel", entity.ApprovalLevel);
            command.Parameters.AddWithValue("@PDFLocation", entity.PDFLocation ?? (object)DBNull.Value);
            
            var id = await command.ExecuteScalarAsync();
            entity.ApplicationID = Convert.ToInt32(id);
            return entity;
        }
        
        public async Task<Application> UpdateAsync(Application entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                UPDATE Applications 
                SET CompanyID = @CompanyID, Description = @Description, Purpose = @Purpose, 
                    RequestedCash = @RequestedCash, IsDisbursed = @IsDisbursed, DisbursedBy = @DisbursedBy,
                    DateDisbursed = @DateDisbursed, Status = @Status, ApprovalLevel = @ApprovalLevel,
                    ApprovalComment = @ApprovalComment, PDFLocation = @PDFLocation
                WHERE ApplicationID = @Id", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Id", entity.ApplicationID);
            command.Parameters.AddWithValue("@CompanyID", entity.CompanyID);
            command.Parameters.AddWithValue("@Description", entity.Description);
            command.Parameters.AddWithValue("@Purpose", entity.Purpose);
            command.Parameters.AddWithValue("@RequestedCash", entity.RequestedCash);
            command.Parameters.AddWithValue("@IsDisbursed", entity.IsDisbursed);
            command.Parameters.AddWithValue("@DisbursedBy", entity.DisbursedBy ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DateDisbursed", entity.DateDisbursed ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Status", entity.Status);
            command.Parameters.AddWithValue("@ApprovalLevel", entity.ApprovalLevel);
            command.Parameters.AddWithValue("@ApprovalComment", entity.ApprovalComment ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@PDFLocation", entity.PDFLocation ?? (object)DBNull.Value);
            
            await command.ExecuteNonQueryAsync();
            return entity;
        }
        
        public async Task<bool> UpdateApprovalLevelAsync(int applicationId, int approvalLevel, string? comment)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                UPDATE Applications 
                SET ApprovalLevel = @ApprovalLevel, ApprovalComment = @Comment
                WHERE ApplicationID = @Id", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Id", applicationId);
            command.Parameters.AddWithValue("@ApprovalLevel", approvalLevel);
            command.Parameters.AddWithValue("@Comment", comment ?? (object)DBNull.Value);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        public async Task<bool> MarkAsDisbursedAsync(int applicationId, string disbursedBy)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                UPDATE Applications 
                SET IsDisbursed = 1, DisbursedBy = @DisbursedBy, DateDisbursed = @DateDisbursed
                WHERE ApplicationID = @Id", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Id", applicationId);
            command.Parameters.AddWithValue("@DisbursedBy", disbursedBy);
            command.Parameters.AddWithValue("@DateDisbursed", DateTime.Now);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("DELETE FROM Applications WHERE ApplicationID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        public async Task<bool> ExistsAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT COUNT(1) FROM Applications WHERE ApplicationID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var count = await command.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        
        private static Application MapToApplication(SqlDataReader reader)
        {
            var application = new Application
            {
                ApplicationID = reader.GetInt32("ApplicationID"),
                CompanyID = reader.GetInt32("CompanyID"),
                Description = reader.GetString("Description"),
                Purpose = reader.GetString("Purpose"),
                RequestedCash = reader.GetDouble("RequestedCash"),
                IsDisbursed = reader.GetBoolean("IsDisbursed"),
                DisbursedBy = reader.IsDBNull("DisbursedBy") ? null : reader.GetString("DisbursedBy"),
                DateRequested = reader.GetDateTime("DateRequested"),
                DateDisbursed = reader.IsDBNull("DateDisbursed") ? null : reader.GetDateTime("DateDisbursed"),
                Status = reader.GetBoolean("Status"),
                ApprovalLevel = reader.GetInt32("ApprovalLevel"),
                ApprovalComment = reader.IsDBNull("ApprovalComment") ? null : reader.GetString("ApprovalComment"),
                PDFLocation = reader.IsDBNull("PDFLocation") ? null : reader.GetString("PDFLocation")
            };
            
            // Set company if available
            if (!reader.IsDBNull("CompanyName"))
            {
                application.Company = new Company
                {
                    CompanyID = application.CompanyID,
                    Name = reader.GetString("CompanyName")
                };
            }
            
            return application;
        }
    }
}
