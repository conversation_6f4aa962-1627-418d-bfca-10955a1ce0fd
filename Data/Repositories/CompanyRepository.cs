using System.Data;
using Microsoft.Data.SqlClient;
using _CashDisbursement.Models.Entities;

namespace _CashDisbursement.Data.Repositories
{
    public interface ICompanyRepository : IRepository<Company>
    {
        Task<Company?> GetByEmailAsync(string email);
        Task<IEnumerable<Company>> GetActiveCompaniesAsync();
        Task<bool> EmailExistsAsync(string email);
    }
    
    public class CompanyRepository : ICompanyRepository
    {
        private readonly IDatabaseConnection _dbConnection;
        
        public CompanyRepository(IDatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }
        
        public async Task<Company?> GetByIdAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT * FROM Company WHERE CompanyID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToCompany(reader);
            }
            return null;
        }
        
        public async Task<Company?> GetByEmailAsync(string email)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT * FROM Company WHERE Email = @Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Email", email);
            
            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return MapToCompany(reader);
            }
            return null;
        }
        
        public async Task<IEnumerable<Company>> GetAllAsync()
        {
            var companies = new List<Company>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT * FROM Company ORDER BY Name", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                companies.Add(MapToCompany(reader));
            }
            return companies;
        }
        
        public async Task<IEnumerable<Company>> GetActiveCompaniesAsync()
        {
            var companies = new List<Company>();
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT * FROM Company WHERE Status = 1 ORDER BY Name", (SqlConnection)connection);
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                companies.Add(MapToCompany(reader));
            }
            return companies;
        }
        
        public async Task<Company> AddAsync(Company entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                INSERT INTO Company (Name, Email, Contact, Address, Status) 
                OUTPUT INSERTED.CompanyID
                VALUES (@Name, @Email, @Contact, @Address, @Status)", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Name", entity.Name);
            command.Parameters.AddWithValue("@Email", entity.Email);
            command.Parameters.AddWithValue("@Contact", entity.Contact ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Address", entity.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Status", entity.Status);
            
            var id = await command.ExecuteScalarAsync();
            entity.CompanyID = Convert.ToInt32(id);
            return entity;
        }
        
        public async Task<Company> UpdateAsync(Company entity)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand(@"
                UPDATE Company 
                SET Name = @Name, Email = @Email, Contact = @Contact, Address = @Address, Status = @Status
                WHERE CompanyID = @Id", (SqlConnection)connection);
            
            command.Parameters.AddWithValue("@Id", entity.CompanyID);
            command.Parameters.AddWithValue("@Name", entity.Name);
            command.Parameters.AddWithValue("@Email", entity.Email);
            command.Parameters.AddWithValue("@Contact", entity.Contact ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Address", entity.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Status", entity.Status);
            
            await command.ExecuteNonQueryAsync();
            return entity;
        }
        
        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("DELETE FROM Company WHERE CompanyID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }
        
        public async Task<bool> ExistsAsync(int id)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT COUNT(1) FROM Company WHERE CompanyID = @Id", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Id", id);
            
            var count = await command.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        
        public async Task<bool> EmailExistsAsync(string email)
        {
            using var connection = await _dbConnection.CreateConnectionAsync();
            var command = new SqlCommand("SELECT COUNT(1) FROM Company WHERE Email = @Email", (SqlConnection)connection);
            command.Parameters.AddWithValue("@Email", email);
            
            var count = await command.ExecuteScalarAsync();
            return Convert.ToInt32(count) > 0;
        }
        
        private static Company MapToCompany(SqlDataReader reader)
        {
            return new Company
            {
                CompanyID = reader.GetInt32("CompanyID"),
                Name = reader.GetString("Name"),
                Email = reader.GetString("Email"),
                Contact = reader.IsDBNull("Contact") ? string.Empty : reader.GetString("Contact"),
                Address = reader.IsDBNull("Address") ? string.Empty : reader.GetString("Address"),
                Status = reader.GetBoolean("Status")
            };
        }
    }
}
